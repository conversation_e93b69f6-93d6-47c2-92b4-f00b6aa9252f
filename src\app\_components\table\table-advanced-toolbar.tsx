"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";

import type { DataTableAdvancedFilterField, DataTableFilterField } from "~/lib/types";
import { cn } from "~/lib/utils";

import { DataTableFilterList } from "~/components/table/table-filter-list";
import { DataTableSortList } from "~/components/table/table-sort-list";
import { DataTableViewOptions } from "~/components/table/table-view-options";

import { Input } from "../ui/input";

interface DataTableAdvancedToolbarProps<TData> extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * The table instance returned from useDataTable hook with pagination, sorting, filtering, etc.
   * @type Table<TData>
   */
  table: Table<TData>;

  /**
   * An array of filter field configurations for the data table.
   * @type DataTableAdvancedFilterField<TData>[]
   * @example
   * const filterFields = [
   *   {
   *     id: 'name',
   *     label: 'Name',
   *     type: 'text',
   *     placeholder: 'Filter by name...'
   *   },
   *   {
   *     id: 'status',
   *     label: 'Status',
   *     type: 'select',
   *     options: [
   *       { label: 'Active', value: 'active', count: 10 },
   *       { label: 'Inactive', value: 'inactive', count: 5 }
   *     ]
   *   }
   * ]
   */
  advancedfilterFields?: DataTableAdvancedFilterField<TData>[];

  /**
   * An array of filter field configurations for the data table.
   * When options are provided, a faceted filter is rendered.
   * Otherwise, a search filter is rendered.
   *
   * @example
   * const filterFields = [
   *   {
   *     id: 'name',
   *     label: 'Name',
   *     placeholder: 'Filter by name...'
   *   },
   *   {
   *     id: 'status',
   *     label: 'Status',
   *     options: [
   *       { label: 'Active', value: 'active', icon: ActiveIcon, count: 10 },
   *       { label: 'Inactive', value: 'inactive', icon: InactiveIcon, count: 5 }
   *     ]
   *   }
   * ]
   */
  filterFields?: DataTableFilterField<TData>[];

  /**
   * Debounce time (ms) for filter updates to enhance performance during rapid input.
   * @default 300
   */
  debounceMs?: number;

  /**
   * Shallow mode keeps query states client-side, avoiding server calls.
   * Setting to `false` triggers a network request with the updated querystring.
   * @default true
   */
  shallow?: boolean;
}

export function DataTableAdvancedToolbar<TData>({
  table,
  filterFields = [],
  advancedfilterFields = [],
  debounceMs = 300,
  shallow = true,
  children,
  className,
  ...props
}: DataTableAdvancedToolbarProps<TData>) {
  // Memoize computation of searchableColumns and filterableColumns
  const { searchableColumns } = React.useMemo(() => {
    return {
      searchableColumns: filterFields.filter((field) => !field.options),
    };
  }, [filterFields]);

  return (
    <div className={cn("flex w-full items-center justify-between gap-2 overflow-auto", className)} {...props}>
      <div className="flex items-center gap-2">
        {searchableColumns.length > 0 &&
          searchableColumns.map(
            (column) =>
              table.getColumn(column.id ? String(column.id) : "") && (
                <Input
                  key={String(column.id)}
                  placeholder={column.placeholder}
                  value={(table.getColumn(String(column.id))?.getFilterValue() as string) ?? ""}
                  onChange={(event) => table.getColumn(String(column.id))?.setFilterValue(event.target.value)}
                  className="h-8 w-40 lg:w-64"
                />
              ),
          )}

        <DataTableFilterList
          table={table}
          filterFields={advancedfilterFields}
          debounceMs={debounceMs}
          shallow={shallow}
        />
        <DataTableSortList table={table} debounceMs={debounceMs} shallow={shallow} />
      </div>

      <div className="flex items-center gap-2">
        {children}
        <DataTableViewOptions table={table} />
      </div>
    </div>
  );
}
