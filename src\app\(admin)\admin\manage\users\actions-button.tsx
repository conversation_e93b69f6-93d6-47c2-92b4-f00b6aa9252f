"use client";

import type { Row } from "@tanstack/react-table";
import { Ellipsis } from "lucide-react";
import React from "react";
import { toast } from "sonner";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

import type { DataTableRowAction } from "~/lib/types";
import type { RouterOutputs } from "~/trpc/react";
import { syncUserRole } from "./actions";

type UserOutput = RouterOutputs["moderator"]["getUsers"]["data"][number];

export function Actions({
  row,
  setRowAction,
}: {
  row: Row<UserOutput>;
  setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<UserOutput> | null>>;
}) {
  const handleSyncRole = React.useCallback(() => {
    toast.promise(syncUserRole({ userId: row.original.userId }), {
      loading: "Đang đồng bộ vai trò...",
      success: "Đã đồng bộ vai trò",
      error: "Đã xảy ra lỗi khi đồng bộ vai trò",
    });
  }, [row.original.userId]);

  const keyDownHandler = React.useCallback(
    (event: React.KeyboardEvent<HTMLDivElement>) => {
      const key = event.key;
      const cmdKey = event.metaKey || event.ctrlKey;

      if (key === "e" && cmdKey) {
        event.preventDefault();
        setRowAction({ row, type: "update" });
      } else if (key === "d" && cmdKey) {
        event.preventDefault();
        setRowAction({ row, type: "delete" });
      } else if (key === "r" && cmdKey) {
        event.preventDefault();
        handleSyncRole();
      }
    },
    [row, setRowAction, handleSyncRole],
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button aria-label="Mở menu" variant="ghost" className="ml-auto flex size-8 p-0 data-[state=open]:bg-muted">
          <Ellipsis className="size-4" aria-hidden="true" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent onKeyDown={keyDownHandler} align="end" className="w-40">
        <DropdownMenuItem onSelect={() => handleSyncRole()} className="focus:cursor-pointer">
          <span>Đồng bộ vai trò</span>
          <DropdownMenuShortcut>⌘+R</DropdownMenuShortcut>
        </DropdownMenuItem>

        <DropdownMenuItem onSelect={() => setRowAction({ row, type: "update" })} className="focus:cursor-pointer">
          <span>Chỉnh sửa</span>
          <DropdownMenuShortcut>⌘+E</DropdownMenuShortcut>
        </DropdownMenuItem>

        <DropdownMenuItem onSelect={() => setRowAction({ row, type: "delete" })} className="focus:cursor-pointer">
          <span>Xóa</span>
          <DropdownMenuShortcut>⌘+D</DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
