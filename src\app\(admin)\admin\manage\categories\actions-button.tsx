"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";

import type { Row } from "@tanstack/react-table";
import { Ellipsis } from "lucide-react";
import React from "react";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

import type { DataTableRowAction } from "~/lib/types";
import type { RouterOutputs } from "~/trpc/react";

type CategoryOutput = RouterOutputs["editor"]["getCategories"]["data"][number];

export function Actions({
  row,
  setRowAction,
}: {
  row: Row<CategoryOutput>;
  setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<CategoryOutput> | null>>;
}) {
  const router = useRouter();

  const keyDownHandler = React.useCallback(
    (event: React.KeyboardEvent<HTMLDivElement>) => {
      const key = event.key;
      const cmdKey = event.metaKey || event.ctrlKey;

      if (key === "v" && cmdKey) {
        event.preventDefault();
        router.push("/category/" + encodeURIComponent(row.original.title));
      } else if (key === "e" && cmdKey) {
        event.preventDefault();
        setRowAction({ row, type: "update" });
      } else if (key === "d" && cmdKey) {
        event.preventDefault();
        setRowAction({ row, type: "delete" });
      }
    },
    [router, row, setRowAction],
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button aria-label="Mở menu" variant="ghost" className="ml-auto flex size-8 p-0 data-[state=open]:bg-muted">
          <Ellipsis className="size-4" aria-hidden="true" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent onKeyDown={keyDownHandler} align="end" className="w-40">
        <DropdownMenuItem>
          <Link className="flex w-full" href={`/category/${encodeURIComponent(row.original.title)}`}>
            <span>Xem</span>
            <DropdownMenuShortcut>⌘+V</DropdownMenuShortcut>
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem onSelect={() => setRowAction({ row, type: "update" })} className="focus:cursor-pointer">
          <span>Chỉnh sửa</span>
          <DropdownMenuShortcut>⌘+E</DropdownMenuShortcut>
        </DropdownMenuItem>

        <DropdownMenuItem onSelect={() => setRowAction({ row, type: "delete" })} className="focus:cursor-pointer">
          <span>Xóa</span>
          <DropdownMenuShortcut>⌘+D</DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
