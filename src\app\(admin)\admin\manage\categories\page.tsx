import { forbidden } from "next/navigation";
import { Suspense } from "react";

import { DataTableSkeleton } from "~/components/table/table-skeleton";
import { Table } from "./table";

import { permission } from "~/lib/permission/server";
import type { SearchParams } from "~/lib/types";
import { categorySearchParamsCache } from "~/lib/validate/admin-category";
import { api } from "~/trpc/server";

export default async function Page(props: { searchParams: Promise<SearchParams> }) {
  const { has } = await permission();
  if (!has({ permissions: ["access:view_category_panel"] })) forbidden();

  const searchParams = categorySearchParamsCache.parse(await props.searchParams);
  const promises = Promise.all([api.editor.getCategories(searchParams)]);

  return (
    <section>
      <Suspense
        fallback={
          <DataTableSkeleton
            columnCount={6}
            searchableColumnCount={1}
            filterableColumnCount={2}
            cellWidths={["10rem", "auto", "12rem", "12rem", "8rem", "8rem"]}
            shrinkZero
          />
        }
      >
        <Table promises={promises} />
      </Suspense>
    </section>
  );
}
