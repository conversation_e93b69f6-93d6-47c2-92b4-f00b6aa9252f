"use client";

import { useRouter } from "next/navigation";

import { parseAsInteger, useQueryState } from "nuqs";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationEnd,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationStart,
} from "../ui/pagination";

import { cn } from "~/lib/utils";

export function UserPagination({ totalPage }: { totalPage: number }) {
  const [pageIndex, setPageIndex] = useQueryState("pageIndex", parseAsInteger.withDefault(1));
  const [pageSize, setPageSize] = useQueryState("pageSize", parseAsInteger.withDefault(5));

  const router = useRouter();

  return (
    <div className="mt-auto flex items-center justify-between">
      <Select
        value={pageSize.toString()}
        onValueChange={async (value) => {
          await setPageSize(Number(value))
            .then(() => setPageIndex(1))
            .then(() => router.refresh());
        }}
      >
        <SelectTrigger className="w-[100px]">
          <SelectValue placeholder={pageSize} />
        </SelectTrigger>

        <SelectContent>
          <SelectItem value="5">5</SelectItem>
          <SelectItem value="10">10</SelectItem>
          <SelectItem value="15">15</SelectItem>
          <SelectItem value="20">20</SelectItem>
        </SelectContent>
      </Select>

      <Pagination className="mx-0 w-max">
        <PaginationContent>
          <PaginationItem className={pageIndex === 1 ? "hover:cursor-not-allowed" : undefined}>
            <PaginationStart
              className={cn({ "pointer-events-none": pageIndex === 1 })}
              href={{ query: { pageIndex: 1, pageSize } }}
            />
          </PaginationItem>

          <PaginationItem className={pageIndex - 1 < 1 ? "hover:cursor-not-allowed" : undefined}>
            <PaginationPrevious
              className={cn({ "pointer-events-none": pageIndex - 1 < 1 })}
              href={{ query: { pageIndex: pageIndex - 1, pageSize } }}
            />
          </PaginationItem>

          <PaginationPageBefore pageIndex={pageIndex} queries={{ pageSize }} />

          <PaginationItem>
            <PaginationLink isActive className="pointer-events-none" href="#">
              {pageIndex}
            </PaginationLink>
          </PaginationItem>

          <PaginationPageAfter pageIndex={pageIndex} totalPages={totalPage} queries={{ pageSize }} />

          <PaginationItem className={pageIndex === totalPage ? "hover:cursor-not-allowed" : undefined}>
            <PaginationNext
              className={cn({ "pointer-events-none": pageIndex === totalPage })}
              href={{ query: { pageIndex: pageIndex + 1, pageSize } }}
            />
          </PaginationItem>

          <PaginationItem className={pageIndex === totalPage ? "hover:cursor-not-allowed" : undefined}>
            <PaginationEnd
              className={cn({ "pointer-events-none": pageIndex === totalPage })}
              href={{ query: { pageIndex: totalPage, pageSize } }}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}
UserPagination.displayName = "UserPagination";

function PaginationPageAfter({
  pageIndex,
  totalPages,
  queries,
}: {
  pageIndex: number;
  totalPages: number;
  queries?: Record<string, unknown>;
}) {
  if (pageIndex >= totalPages) return null;

  const maxNextPages = Math.min(totalPages - pageIndex, 3);
  const endPage = pageIndex + maxNextPages;

  return (
    <>
      {Array.from({ length: maxNextPages }, (_, index) => {
        const pageNum = pageIndex + index + 1;
        return (
          <PaginationItem key={`user-pagination-next-${pageNum}`}>
            <PaginationLink href={{ query: { pageIndex: pageNum, ...queries } }}>{pageNum}</PaginationLink>
          </PaginationItem>
        );
      })}

      {endPage < totalPages && (
        <PaginationItem>
          <PaginationEllipsis />
        </PaginationItem>
      )}
    </>
  );
}
PaginationPageAfter.displayName = "PaginationPageAfter";

function PaginationPageBefore({ pageIndex, queries }: { pageIndex: number; queries?: Record<string, unknown> }) {
  if (pageIndex - 1 < 1) return null;

  const maxPreviousPages = Math.min(pageIndex - 1, 3);
  const startPage = pageIndex - maxPreviousPages;

  return (
    <>
      {pageIndex > 4 && (
        <PaginationItem>
          <PaginationEllipsis />
        </PaginationItem>
      )}

      {Array.from({ length: maxPreviousPages }, (_, index) => {
        const pageNum = startPage + index;

        return (
          <PaginationItem key={`user-pagination-${pageNum}`}>
            <PaginationLink href={{ query: { pageIndex: pageNum, ...queries } }}>{pageNum}</PaginationLink>
          </PaginationItem>
        );
      })}
    </>
  );
}
PaginationPageBefore.displayName = "PaginationPageBefore";
