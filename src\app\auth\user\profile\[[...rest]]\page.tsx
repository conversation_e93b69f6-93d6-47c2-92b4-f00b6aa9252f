"use client";

import "~/styles/clerk-shadcn.css";
import { UserProfile } from "@clerk/nextjs";
import { CreditCardIcon } from "lucide-react";

export default function Page() {
  return (
    <UserProfile path="/auth/user/profile" routing="path">
      {/* You can also pass the content as direct children */}
      <UserProfile.Page label="Plans & Billings" labelIcon={<CreditCardIcon size="16" />} url="billings">
        <div>
          <h1>Custom Terms Page</h1>
          <p>This is the custom terms page</p>
        </div>
      </UserProfile.Page>
    </UserProfile>
  );
}
