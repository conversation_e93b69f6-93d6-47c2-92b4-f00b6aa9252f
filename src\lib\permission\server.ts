import "server-only";

import { type Permission, type Roles, permission as clientPermission } from ".";

import { auth } from "@clerk/nextjs/server";

type HasRoles = { roles?: Roles | Roles[] };
type HasPermissions = { permissions?: Permission | Permission[] };

/**
 * Get permission for the current user, user is optional
 * If user is not provided, it will get the current user
 * UserId will be null if user isn't logged in
 *
 * @param user The user to get permission for
 * @returns Permission object
 */
export async function permission(user?: UserPublicMetadata) {
  user ??= (await auth())?.sessionClaims?.metadata;

  return {
    has: (data: HasRoles | HasPermissions) => clientPermission.has({ ...data, user }),
    getUserViewableContent: () => clientPermission.getUserViewableContent({ user }),
    isUserAllowed: (role: Roles) => clientPermission.isUserAllowed({ role, user }),

    userId: user?.id,
  };
}
