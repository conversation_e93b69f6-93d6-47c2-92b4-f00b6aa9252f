"use client";

import { use, useMemo, useState } from "react";

import { BaseTable } from "~/components/table/base-table";
import { TableHeaderFeature } from "~/components/table/features/table-header-title";
import { DataTableAdvancedToolbar } from "~/components/table/table-advanced-toolbar";

import { getColumns, headersToTitle } from "./columns";
import { UpdateCategorySheet } from "./update-sheet";
import { DeleteCategoriesDialog } from "./delete-category";

import { type RouterOutputs } from "~/trpc/react";

import { useDataTable } from "~/lib/hooks/use-data-table";
import type { DataTableAdvancedFilterField, DataTableFilterField, DataTableRowAction } from "~/lib/types";

type Props<TOutput extends RouterOutputs["moderator"] = RouterOutputs["moderator"]> = {
  promises: Promise<[TOutput["getUsers"]]>;
  children?: React.ReactNode;
};

export function Table(props: Props) {
  const [{ data, pageCount, total }] = use(props.promises);
  const [rowAction, setRowAction] = useState<DataTableRowAction<(typeof data)[number]> | null>(null);

  const columns = useMemo(() => getColumns({ setRowAction }), []);

  const filterFields: DataTableFilterField<(typeof data)[number]>[] = [
    {
      id: "username",
      label: "Người dùng",
      placeholder: "Tìm kiếm người dùng",
    },
  ];

  const advancedFilterFields: DataTableAdvancedFilterField<(typeof data)[number]>[] = [
    {
      id: "articleCount",
      label: "Tổng bản tin",
      type: "number",
    },
    {
      id: "createdAt",
      label: "Ngày tạo",
      type: "date",
    },
    {
      id: "updatedAt",
      label: "Ngày cập nhật",
      type: "date",
    },
  ];

  const { table } = useDataTable({
    _features: [TableHeaderFeature],
    columns: columns,
    data: data,
    pageCount: pageCount,
    filterFields,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
      headerTitle: headersToTitle,
      total: total,
    },
    getRowId: (originalRow) => originalRow.userId,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <BaseTable table={table}>
        <DataTableAdvancedToolbar
          table={table}
          filterFields={filterFields}
          advancedfilterFields={advancedFilterFields}
          shallow={false}
        >
          {props.children}
        </DataTableAdvancedToolbar>
      </BaseTable>
    </>
  );
}
