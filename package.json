{"name": "detai-cnpm-next-15", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build --turbopack", "check": "next lint && tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push --verbose --strict", "db:studio": "drizzle-kit studio", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsgo --noEmit"}, "dependencies": {"@clerk/clerk-sdk-node": "^5.1.6", "@clerk/elements": "^0.23.46", "@clerk/localizations": "^3.20.4", "@clerk/nextjs": "^6.27.1", "@clerk/themes": "^2.4.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^5.2.0", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-icons": "^1.3.2", "@t3-oss/env-nextjs": "^0.13.8", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@trpc/client": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "@xstate/store": "^3.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.3", "lucide-react": "^0.526.0", "next": "^15.4.2-canary.18", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "postgres": "^3.4.7", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-day-picker": "9.8.1", "react-dom": "^19.1.0", "react-hook-form": "^7.61.1", "react-markdown": "^10.1.0", "server-only": "^0.0.1", "sonner": "^2.0.6", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.0", "vaul": "^1.1.2", "zod": "^4.0.10"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/eslint": "^8.56.10", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/turndown": "^5.0.5", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@typescript/native-preview": "^7.0.0-dev.20250727.1", "babel-plugin-react-compiler": "^19.1.0-rc.2", "drizzle-kit": "^0.31.4", "eslint": "^8.57.0", "eslint-config-next": "^15.4.4", "eslint-plugin-drizzle": "^0.2.3", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.11", "typescript": "^5.9.0-beta"}, "ct3aMetadata": {"initVersion": "7.38.0"}, "trustedDependencies": ["@clerk/shared"], "packageManager": "bun@1.2.19"}