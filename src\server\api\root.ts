import { createCallerFactory, createTR<PERSON><PERSON>out<PERSON> } from "./trpc";

import { commentRouter } from "./routers/comment";
import { editorRouter } from "./routers/editor";
import { moderatorRouter } from "./routers/moderator";
import { userRouter } from "./routers/user";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  user: userRouter,
  comment: commentRouter,
  editor: editorRouter,
  moderator: moderatorRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
