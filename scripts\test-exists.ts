import { eq, sql } from "drizzle-orm";
import { db, schema } from "~/server/db";

const categoryIds: { id: string; result: boolean }[] = [
  { id: "hhsyazhptk7w83fmx5taqb8m", result: true },
  { id: "ukiyaq1dscb645m77wfnlln9", result: true },
  { id: "k9sjvvc8br5bep7rqrzhnhhz", result: true },
  { id: "py3y7like0qzwc895360dwdg", result: true },
  { id: "b2t3dxzyhi42mbrsesziiagt", result: true },
  { id: "s64uz49tcohk2f16pmfaysto", result: true },
  { id: "t0qi8qgf761lhbhvke0w7e59", result: true },
  { id: "hmii0rzzkve2xr7oe5ky41yu", result: true },
  { id: "jkp7kzv1x2m3t2eb9k98dn2a", result: true },
  { id: "i5ivrttbrhsk2n8mw784avzk", result: true },
  { id: "c10p34wotrzs8euiwddl0lt4", result: true },
  { id: "dkfbl6jjnawvznhd74azok6g", result: true },
  { id: "qyrq0qwlpu53pf53rosvmjkn", result: true },
  { id: "zh3ht6f5aih0wnwf94646uut", result: true },
  { id: "ynft1mmhugnlgu0cdvs63199", result: true },
  { id: "xdw0m78opneg609mty342n3t", result: true },

  { id: "qsaht8wm5z3k7hfytpamb8xh", result: false },
  { id: "bnsy5k1lln47quw79amicd6f", result: false },
  { id: "rvzbrk5shcven7r9bqzphjh8", result: false },
  { id: "0gwed5yqci78p9yld330k6wz", result: false },
  { id: "t2ea3gxrtibh2i4ibzzdsysm", result: false },
  { id: "26zph4ysto69cmoaf4uk1tfs", result: false },
  { id: "5vw7h6bk7qe1tf0l0e8qgih9", result: false },
  { id: "14rheuo27ir5mzkxzyyevi0k", result: false },
  { id: "k712kdtx32kbme892j9pzvan", result: false },
  { id: "zmikk88rnvis5ta4rvw7b2ht", result: false },
  { id: "00w84le1uldsptzc4wr3idto", result: false },
  { id: "kdowadn6jkfj7hlzbva46zng", result: false },
  { id: "j3srfkv0y5wm5oqqun3lrqpp", result: false },
  { id: "zw56it06tnfu49ah3wu46fhh", result: false },
  { id: "0tgc9yh693m1ugufl1nsvndm", result: false },
  { id: "8xmdn6ew7m3320pgnott4y09", result: false },
].sort(() => Math.random() - 0.5);

async function runTest({ func, name }: { name: string; func: (id: string) => Promise<boolean> }) {
  const start = performance.now();
  for (const { id, result: expected } of categoryIds) {
    const result = await func(id);

    if (result !== expected) {
      console.error(`Test failed for ${name} with id ${id}`);
    }
  }
  const end = performance.now();

  console.log(`${name} took ${end - start}ms`);
}

const testList: { name: string; func: (id: string) => Promise<boolean> }[] = [
  {
    name: "Raw SQL EXISTS SELECT 1",
    func: async (id) => {
      const result = await db.execute(
        sql`SELECT EXISTS(SELECT 1 FROM ${schema.category} WHERE ${schema.category.categoryId} = ${id})`,
      );

      return (result[0] as { exists: boolean }).exists;
    },
  },
  {
    name: "Count with WHERE",
    func: async (id) => {
      const result = await db.$count(schema.category, eq(schema.category.categoryId, id));
      return result > 0;
    },
  },
  {
    name: "Find First",
    func: async (id) => {
      const result = await db.query.category.findFirst({
        where: (table, { eq }) => eq(table.categoryId, id),
      });

      return !!result;
    },
  },
];

for (const test of testList) {
  await runTest(test);

  await new Promise((resolve) => setTimeout(resolve, 2000));
}

process.exit(0);
