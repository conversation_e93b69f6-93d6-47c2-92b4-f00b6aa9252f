"use server";

import "server-only";

import { eq, inArray } from "drizzle-orm";
import { unstable_noStore } from "next/cache";

import { db, schema } from "~/server/db";

import { getErrorMessage } from "~/lib/handle-error";
import { permission } from "~/lib/permission/server";
import type { UpdateCategorySchema } from "~/lib/validate/admin-category";

export async function updateCategory(input: UpdateCategorySchema & { categoryId: string }) {
  unstable_noStore();

  const { has } = await permission();
  if (!has({ permissions: "category:edit" })) {
    return { data: null, error: "Bạn không có quyền chỉnh sửa danh mục này" };
  }

  try {
    await db
      .update(schema.category)
      .set({ title: input.title, description: input.description })
      .where(eq(schema.category.categoryId, input.categoryId));

    return { data: null, error: null };
  } catch (err) {
    return { data: null, error: getErrorMessage(err) };
  }
}

export async function deleteCategory(input: { categoryId: string }) {
  unstable_noStore();

  const { has } = await permission();
  if (!has({ permissions: "category:delete" })) {
    return { data: null, error: "Bạn không có quyền xóa danh mục này" };
  }

  try {
    await db.delete(schema.category).where(eq(schema.category.categoryId, input.categoryId));

    return { data: null, error: null };
  } catch (err) {
    return { data: null, error: getErrorMessage(err) };
  }
}

export async function deleteCategories(input: { categoryIds: string[] }) {
  unstable_noStore();

  const { has } = await permission();
  if (!has({ permissions: "category:delete" })) {
    return { data: null, error: "Bạn không có quyền xóa các danh mục này" };
  }

  try {
    await db.delete(schema.category).where(inArray(schema.category.categoryId, input.categoryIds));

    return { data: null, error: null };
  } catch (err) {
    return { data: null, error: getErrorMessage(err) };
  }
}
