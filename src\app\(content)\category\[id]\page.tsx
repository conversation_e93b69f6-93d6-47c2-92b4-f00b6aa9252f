import { notFound } from "next/navigation";

import { ArticleDisplay } from "~/app/_components/article-display";
import { permission } from "~/lib/permission/server";

import { db, schema } from "~/server/db";

export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ pageIndex: string }>;
}) {
  const categoryTitle = decodeURIComponent((await params).id).replace(/-/, " ");
  const pageIndex = Number((await searchParams).pageIndex);
  const viewableContent = (await permission()).getUserViewableContent();

  const category = await db.query.category.findFirst({
    columns: { title: true, categoryId: true },
    where: (table, { eq }) => eq(table.title, categoryTitle),
    extras: (table, { sql }) => ({
      totalArticles: db
        .$count(
          sql`${schema.article} "a"`,
          sql`"a"."categoryId" = ${table.categoryId} AND "a"."status" IN ${viewableContent}`,
        )
        .as("totalArticles"),
    }),
  });

  if (!category) return notFound();

  const data = await db.query.article.findMany({
    where: (table, { inArray, eq, and }) =>
      and(eq(table.categoryId, category.categoryId), inArray(table.status, viewableContent)),
    orderBy: (table, { desc }) => desc(table.createdAt),
    offset: pageIndex * 20,
    limit: 20,
    extras: (table, { sql }) => ({
      favoriteCount: db
        .$count(sql`${schema.favorite} "f"`, sql`"f"."articleId" = ${table.articleId}`)
        .as("favoriteCount"),
    }),
  });

  return (
    <main className="container py-4">
      <h1 className="mb-4 text-2xl font-bold">Bản tin về {category.title}</h1>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {data.map((article) => (
          <ArticleDisplay article={article} key={article.articleId} />
        ))}
      </div>
    </main>
  );
}
