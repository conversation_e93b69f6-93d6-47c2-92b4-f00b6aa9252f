"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";

import { useClerk } from "@clerk/nextjs";

import { LogIn, LogOut } from "lucide-react";

export const SignOutBtn = () => {
  const { signOut } = useClerk();
  const router = useRouter();

  return (
    <button
      onMouseDown={() => signOut().finally(() => router.refresh())}
      className="flex w-full items-center gap-2 rounded px-2 py-1.5"
    >
      <LogOut size={16} />
      <span>Đăng xuất</span>
    </button>
  );
};

export const SignInBtn = () => {
  const pathname = usePathname();

  return (
    <Link
      href={{ pathname: "/auth/sign-in", query: { redirect_url: pathname } }}
      className="flex w-full items-center gap-2 px-2 py-1.5"
    >
      <LogIn size={16} />
      <PERSON><PERSON><PERSON> nh<PERSON>p
    </Link>
  );
};
