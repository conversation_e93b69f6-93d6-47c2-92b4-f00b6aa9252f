import { and, asc, count, desc, eq, getTableColumns, gte, ilike, lte, or, sql } from "drizzle-orm";
import { z } from "zod";
import { dataTableConfig } from "~/lib/data-table-config";

import { filterColumns } from "~/lib/filter-columns";
import { ObjectTypeSafe } from "~/lib/utils";

import { CreateArticleSchema } from "~/lib/validate/admin-article";
import { createTRPCRouter, editorProcedure } from "~/server/api/trpc";
import { schema, type Schema } from "~/server/db";
import { article } from "~/server/db/schema";

export const editorRouter = createTRPCRouter({
  createArticle: editorProcedure.input(CreateArticleSchema).mutation(async ({ ctx, input }) => {
    await ctx.db.insert(article).values({
      ...input,
      authorId: ctx.user.userId,
    });
  }),

  getArticleById: editorProcedure.input(z.object({ articleId: z.string() })).query(async ({ ctx, input }) => {
    const allowToSeeAllArticles = ctx.user.permissions.has({ permissions: "content:edit_all" });

    const articleData = await ctx.db.query.article.findFirst({
      where: (table, { eq, and }) =>
        and(
          eq(table.articleId, input.articleId),
          allowToSeeAllArticles ? undefined : eq(table.authorId, ctx.user.userId),
        ),
      with: {
        author: { columns: { imageUrl: true, username: true } },
        category: { columns: { title: true } },
      },
    });

    return articleData;
  }),
  getAllCategories: editorProcedure.query(async ({ ctx }) => {
    try {
      const allowToSeeAllArticles = ctx.user.permissions.has({ permissions: "content:edit_all" });

      return await ctx.db.query.category.findMany({
        extras: (table, { sql }) => ({
          articleCount: ctx.db
            .$count(
              sql`${ctx.schema.article} "a"`,
              and(
                sql`${table.categoryId} = "a"."categoryId"`,
                allowToSeeAllArticles ? undefined : eq(sql`"a"."authorId"`, ctx.user.userId),
              ),
            )
            .as("articleCount"),
        }),
      });
    } catch (error) {
      console.error(error);
      return [];
    }
  }),

  getStatusCounts: editorProcedure.query(async ({ ctx }) => {
    type Output = Record<Schema["article"]["$inferSelect"]["status"], number>;

    try {
      const allowToSeeAllArticles = ctx.user.permissions.has({ permissions: "content:edit_all" });
      const article = ctx.schema.article;

      return await ctx.db
        .select({
          count: count(),
          status: article.status,
        })
        .from(article)
        .where(allowToSeeAllArticles ? undefined : eq(article.authorId, ctx.user.userId))
        .groupBy(article.status)
        .having(gte(count(), 1))
        .then((res) =>
          res.reduce((acc, { status, count }) => {
            acc[status] = count;
            return acc;
          }, {} as Output),
        );
    } catch (error) {
      console.error(error);
      return {} as Output;
    }
  }),

  getArticles: editorProcedure
    .input(
      z.object({
        pageIndex: z.coerce.number().min(0).catch(0),
        pageSize: z.coerce.number().catch(10),

        title: z.string().optional(),
        filters: z.array(
          z.object({
            id: z.enum(ObjectTypeSafe.keys(getTableColumns(schema.article))),
            value: z.union([z.string(), z.array(z.string())]),
            operator: z.enum(dataTableConfig.globalOperators),
            type: z.enum(dataTableConfig.columnTypes),
            rowId: z.string(),
          }),
        ),
        sort: z.object({ id: z.string(), desc: z.boolean() }).array().nullish(),
        from: z.string().optional(),
        to: z.string().optional(),

        joinOperator: z.enum(["and", "or"]).default("and"),
      }),
    )
    .query(async ({ ctx, input }) => {
      try {
        const allowToSeeAllArticles = ctx.user.permissions.has({ permissions: "content:edit_all" });

        const offset = (input.pageIndex - 1) * input.pageSize;
        const fromDate = input.from ? new Date(input.from) : undefined;
        const toDate = input.to ? new Date(input.to) : undefined;

        const article = ctx.schema.article;

        const advancedWhere = filterColumns({
          table: article,
          filters: input.filters.filter((item) => item.value),
          joinOperator: input.joinOperator,
        });

        const where = and(
          input.title ? ilike(article.title, `%${input.title}%`) : undefined,
          advancedWhere,

          fromDate ? gte(article.createdAt, fromDate) : undefined,
          toDate ? lte(article.createdAt, toDate) : undefined,
          allowToSeeAllArticles ? undefined : eq(article.authorId, ctx.user.userId),
        );

        const data = await ctx.db.query.article.findMany({
          limit: input.pageSize,
          offset: offset,
          where,
          orderBy: (table, { desc, asc }) => {
            return input.sort && input.sort.length > 0
              ? input.sort.map((item) => {
                  const id = item.id as keyof typeof table;
                  return item.desc ? desc(table[id]) : asc(table[id]);
                })
              : [asc(table.createdAt)];
          },

          with: {
            author: { columns: { imageUrl: true, username: true } },
            category: { columns: { title: true } },
          },
        });

        const total = await ctx.db.$count(ctx.schema.article, where);
        const pageCount = Math.ceil(total / input.pageSize);

        return { data, pageCount };
      } catch (error) {
        console.error(error);
        return { data: [], pageCount: 0 };
      }
    }),

  getCategories: editorProcedure
    .input(
      z.object({
        pageIndex: z.coerce.number().min(0).catch(0),
        pageSize: z.coerce.number().catch(10),

        title: z.string().optional(),
        filters: z.array(
          z.object({
            id: z.enum([
              ...ObjectTypeSafe.keys(getTableColumns(schema.category)),
              "articleCount",
              "viewCount",
              "favoriteCount",
            ]),
            value: z.union([z.string(), z.array(z.string())]),
            operator: z.enum(dataTableConfig.globalOperators),
            type: z.enum(dataTableConfig.columnTypes),
            rowId: z.string(),
          }),
        ),
        sort: z.object({ id: z.string(), desc: z.boolean() }).array().nullish(),
        joinOperator: z.enum(["and", "or"]).default("and"),
      }),
    )
    .query(async ({ ctx, input }) => {
      try {
        const offset = (input.pageIndex - 1) * input.pageSize;
        const { category: c, article: a, favorite: f } = ctx.schema;

        const advancedWhere = filterColumns({
          table: c,
          // @ts-expect-error Doesn't have a way to include custom columns
          filters: input.filters.filter((item) => item.value),
          joinOperator: input.joinOperator,
        });

        const where = and(
          or(
            input.title ? ilike(sql`"category_status"."title"`, `%${input.title}%`) : undefined,
            input.title ? ilike(sql`"category_status"."description"`, `%${input.title}%`) : undefined,
          ),
          advancedWhere,
        );

        const subQuery = ctx.db
          .select({
            ...getTableColumns(c),
            articleCount: ctx.db.$count(a, eq(c.categoryId, a.categoryId)).as("articleCount"),
            viewCount: sql<number>`(SELECT SUM(${a.views}) FROM ${a} WHERE ${eq(c.categoryId, a.categoryId)})`.as(
              "viewCount",
            ),
            favoriteCount:
              sql<number>`(SELECT COUNT(${f.userId}) FROM ${f} LEFT JOIN ${a} ON ${eq(a.articleId, f.articleId)} WHERE ${eq(c.categoryId, a.categoryId)})`.as(
                "favoriteCount",
              ),
          })
          .from(c)
          .as("category_status");

        const orderBy =
          input.sort && input.sort.length > 0
            ? input.sort.map((item) => {
                const field = sql.raw(`"category_status"."${item.id}"`);
                return item.desc ? desc(field) : asc(field);
              })
            : [asc(sql`"category_status"."createdAt"`)];

        const data = ctx.db
          .select()
          .from(subQuery)
          .where(where)
          .limit(input.pageSize)
          .offset(offset)
          .orderBy(...orderBy);

        console.log(data.toSQL());

        const total = await ctx.db.$count(subQuery, where);
        const pageCount = Math.ceil(total / input.pageSize);

        return { data: await data, pageCount, total };
      } catch (error) {
        console.error(error);
        return { data: [], pageCount: 0 };
      }
    }),
});
