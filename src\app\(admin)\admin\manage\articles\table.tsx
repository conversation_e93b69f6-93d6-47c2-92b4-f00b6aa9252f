"use client";

import { use, useMemo } from "react";

import { BaseTable } from "~/components/table/base-table";
import { DataTableAdvancedToolbar } from "~/components/table/table-advanced-toolbar";
import { TableHeaderFeature } from "~/app/_components/table/features/table-header-title";

import { getColumns, headersToTitle } from "./columns";

import { article } from "~/server/db/schema";
import { type RouterOutputs } from "~/trpc/react";

import { useDataTable } from "~/lib/hooks/use-data-table";
import type { DataTableAdvancedFilterField, DataTableFilterField } from "~/lib/types";
import { toSentenceCase } from "~/lib/utils";

type Props<TOutput extends RouterOutputs["editor"] = RouterOutputs["editor"]> = {
  promises: Promise<[TOutput["getArticles"], TOutput["getAllCategories"], TOutput["getStatusCounts"]]>;
  children?: React.ReactNode;
};

export function Table(props: Props) {
  const [{ data, pageCount }, categories, statusCount] = use(props.promises);
  const columns = useMemo(() => getColumns(), []);

  const filterFields: DataTableFilterField<(typeof data)[number]>[] = [
    {
      id: "title",
      label: "Title",
      placeholder: "Filter titles...",
    },
  ];

  const advancedFilterFields: DataTableAdvancedFilterField<(typeof data)[number]>[] = [
    {
      id: "status",
      label: "Status",
      type: "multi-select",
      options: article.status.enumValues.map((status) => ({
        label: toSentenceCase(status),
        value: status,
        count: statusCount[status],
      })),
    },
    {
      id: "categoryId",
      label: "Category",
      type: "multi-select",
      options: categories.map((item) => ({
        label: item.title,
        value: item.categoryId,
        count: item.articleCount,
      })),
    },
    {
      id: "createdAt",
      label: "Created At",
      type: "date",
    },
  ];

  const { table } = useDataTable({
    _features: [TableHeaderFeature],
    columns: columns,
    data: data,
    pageCount: pageCount,
    filterFields,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
      headerTitle: headersToTitle,
    },
    getRowId: (originalRow) => originalRow.articleId,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <BaseTable table={table}>
      <DataTableAdvancedToolbar
        table={table}
        filterFields={filterFields}
        advancedfilterFields={advancedFilterFields}
        shallow={false}
      >
        {props.children}
      </DataTableAdvancedToolbar>
    </BaseTable>
  );
}
