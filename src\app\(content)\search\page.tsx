import { redirect } from "next/navigation";

import { eq } from "drizzle-orm";

import { ArticleDisplay } from "~/app/_components/article-display";

import { permission } from "~/lib/permission/server";
import { db, schema } from "~/server/db";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<{ query?: string; pageIndex?: number }>;
}) {
  const { query: rawQuery, pageIndex = 0 } = await searchParams;
  const viewableContent = (await permission()).getUserViewableContent();

  if (!rawQuery) redirect("/");
  const query = "%" + rawQuery.trim() + "%";

  const data = await db.query.article.findMany({
    limit: 20,
    offset: pageIndex * 20,
    orderBy: (table, { desc }) => desc(table.createdAt),
    where: (table, { or, ilike, inArray, and }) =>
      and(
        or(ilike(table.title, query), ilike(table.summary, query), ilike(table.articleId, query)),
        inArray(table.status, viewableContent),
      ),
    extras: (table, { sql }) => ({
      favoriteCount: db
        .$count(sql`${schema.favorite} "f"`, eq(sql`"f"."articleId"`, table.articleId))
        .as("favoriteCount"),
    }),
  });

  return (
    <main className="container py-4">
      <h1 className="mb-4 text-2xl font-bold">Bản tin về {rawQuery}</h1>

      <section className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {data.map((article) => (
          <ArticleDisplay article={article} key={article.articleId} />
        ))}
      </section>
    </main>
  );
}
