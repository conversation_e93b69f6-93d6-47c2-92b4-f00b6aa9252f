# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.

# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# Drizzle environment variables for paths
DATABASE_URL = "postgresql://...:6543/postgres"

# Clerk environment variables for paths
NEXT_PUBLIC_CLERK_SIGN_IN_URL = "/auth/sign-in"
NEXT_PUBLIC_CLERK_SIGN_UP_URL = "/auth/sign-up"
NEXT_PUBLIC_CLERK_USER_PROFILE_URL = "/auth/user/profile"
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL = "/"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL = "/"

# Clerk keys
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY = "pk_test_"
CLERK_SECRET_KEY = "sk_test_"
CLERK_SIGNING_KEY = "whsec_"
