import { eq } from "drizzle-orm";
import { z } from "zod";

import { authProcedure, createTR<PERSON>Router, publicProcedure } from "~/server/api/trpc";

export const commentRouter = createTRPCRouter({
  fetchTotalComments: publicProcedure.input(z.object({ articleId: z.string() })).query(async ({ ctx, input }) => {
    return ctx.db.$count(ctx.schema.comment, eq(ctx.schema.comment.articleId, input.articleId));
  }),

  fetchCommentsForArticle: publicProcedure
    .input(
      z.object({
        articleId: z.string(),
        pageIndex: z.coerce.number().min(0).catch(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      const data = await ctx.db.query.comment.findMany({
        where: (table, { eq, and, isNull }) => and(eq(table.articleId, input.articleId), isNull(table.replyId)),
        orderBy: (table, { desc }) => desc(table.createdAt),
        limit: 20,
        offset: input.pageIndex * 20,
        with: { user: { columns: { userId: true, username: true, imageUrl: true } } },
        extras: (table, { sql }) => ({
          repliesCount: ctx.db
            .$count(sql`${ctx.schema.comment} "c"`, eq(sql`"c"."replyId"`, table.commentId))
            .as("repliesCount"),
        }),
      });

      return data;
    }),

  fetchReplies: publicProcedure.input(z.object({ commentId: z.string() })).query(async ({ ctx, input }) => {
    return ctx.db.query.comment.findMany({
      where: (table, { eq }) => eq(table.replyId, input.commentId),
      with: { user: { columns: { userId: true, username: true, imageUrl: true } } },
      orderBy: (table, { desc }) => desc(table.createdAt),
      extras: (table, { sql }) => ({
        repliesCount: ctx.db
          .$count(sql`${ctx.schema.comment} "c"`, eq(sql`"c"."replyId"`, table.commentId))
          .as("repliesCount"),
      }),
    });
  }),

  postComment: authProcedure
    .input(z.object({ articleId: z.string(), content: z.string(), replyId: z.string().nullish() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.insert(ctx.schema.comment).values({
        articleId: input.articleId,
        content: input.content,
        replyId: input.replyId,
        userId: ctx.user.userId,
      });
    }),
});
