# Bản Tin 24H - <PERSON><PERSON><PERSON><PERSON> <PERSON> Tức Đa Dạng và Chính Xác

Bản Tin 24H là một trang web tin tức tiếng Việt cung cấp nội dung đa dạng và chính xác. Dự án được xây dựng bằng Next.js, TypeScript và Tailwind CSS, sử dụng các công nghệ hiện đại để mang đến trải nghiệm tốt nhất cho người dùng.

## Công Nghệ Sử Dụng

- [Next.js](https://nextjs.org) - Framework React cho render phía server và tĩnh.
- [TypeScript](https://www.typescriptlang.org) - Ngôn ngữ lập trình mở rộng từ JavaScript.
- [Tailwind CSS](https://tailwindcss.com) - Framework CSS tiện ích cho việc thiết kế nhanh chóng.
- [Clerk](https://clerk.dev) - Quản lý người dùng và xác thực.
- [Drizzle ORM](https://github.com/drizzle-team/drizzle-orm) - ORM an toàn cho PostgreSQL.
- [shadcn/ui](https://ui.shadcn.com) - Bộ component đẹp mắt xây dựng trên Radix UI và Tailwind CSS.

## Cài Đặt

### Yêu Cầu

- Node.js >= **22.x.x**

### Hướng Dẫn

1. **Clone Repository**

   ```sh
   git clone https://github.com/your_username/your_repository.git
   ```

2. **Cài Đặt Phụ Thuộc**

   Chuyển đến thư mục dự án và cài đặt các gói cần thiết.

   ```sh
   cd detai-cnpm-next-15
   npm install
   ```

3. **Cấu Hình Biến Môi Trường**

   Tạo file .env trong thư mục gốc dựa trên file .env.example và điền thông tin cần thiết.

   ```sh
   cp .env.example .env
   ```

Cung cấp chuỗi kết nối cơ sở dữ liệu và các khóa API của Clerk.

4. **Chạy Migrations Cơ Sở Dữ Liệu**

   Thực thi migrations để thiết lập schema cho PostgreSQL.

   ```sh
   npm run db:migrate
   ```

## Sử Dụng

### Chạy Ứng Dụng Ở Chế Độ Phát Triển

Khởi động server phát triển:

```sh
npm run dev
```

Ứng dụng sẽ chạy tại `http://localhost:3000`.

### Xây Dựng Cho Sản Xuất

Để xây dựng ứng dụng cho môi trường sản xuất, chạy:

```sh
npm run build
```

Sau đó, khởi động server sản xuất:

```sh
npm start
```

## Đóng Góp

Chúng tôi hoan nghênh mọi đóng góp! Vui lòng mở issue hoặc gửi pull request để cải thiện dự án.

## Giấy Phép

Dự án này được cấp phép dưới giấy phép MIT.

## Ghi Nhận

- [Next.js](https://nextjs.org)
- [Tailwind CSS](https://tailwindcss.com)
- [Clerk](https://clerk.dev)
- [Drizzle ORM](https://github.com/drizzle-team/drizzle-orm)
- [shadcn/ui](https://ui.shadcn.com)
