"use client";

import { useRouter } from "next/navigation";

import { zodResolver } from "@hookform/resolvers/zod";
import { Loader } from "lucide-react";
import * as React from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "~/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "~/components/ui/sheet";
import { Textarea } from "~/components/ui/textarea";

import { type UpdateCategorySchema, updateCategorySchema } from "~/lib/validate/admin-category";
import type { category } from "~/server/db/schema";
import { updateUser } from "./actions";

interface UpdateTaskSheetProps extends React.ComponentPropsWithRef<typeof Sheet> {
  category: (typeof category)["$inferSelect"] | null;
}

export function UpdateCategorySheet({ category, ...props }: UpdateTaskSheetProps) {
  const [isUpdatePending, startUpdateTransition] = React.useTransition();
  const router = useRouter();

  const form = useForm<UpdateCategorySchema>({
    resolver: zodResolver(updateCategorySchema),
    defaultValues: {
      title: category?.title ?? "",
      description: category?.description ?? "",
    },
  });

  function onSubmit(input: UpdateCategorySchema) {
    startUpdateTransition(async () => {
      if (!category) return;

      const { error } = await updateUser({
        categoryId: category.categoryId,
        ...input,
      });

      if (error) {
        toast.error(error);
        return;
      }

      form.reset();
      props.onOpenChange?.(false);
      toast.success("Danh mục đã được cập nhật");

      router.refresh();
    });
  }

  return (
    <Sheet {...props}>
      <SheetContent className="flex flex-col gap-6 sm:max-w-md">
        <SheetHeader className="text-left">
          <SheetTitle>Cập nhật danh mục</SheetTitle>
          <SheetDescription>Cập nhật chi tiết danh mục và lưu thay đổi</SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tiêu đề</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Nhập tiêu đề danh mục" className="resize-none" {...field} />
                  </FormControl>
                  <FormDescription>Đây sẽ là tiêu đề hiển thị cho danh mục.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mô tả</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Nhập mô tả danh mục" className="resize-none" {...field} />
                  </FormControl>
                  <FormDescription>Nhập mô tả chi tiết cho danh mục.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <SheetFooter className="gap-2 pt-2 sm:space-x-0">
              <SheetClose asChild>
                <Button type="button" variant="outline">
                  Hủy
                </Button>
              </SheetClose>

              <Button type="submit" disabled={isUpdatePending}>
                {isUpdatePending && <Loader className="mr-2 size-4 animate-spin" aria-hidden="true" />}
                Lưu
              </Button>
            </SheetFooter>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
}
