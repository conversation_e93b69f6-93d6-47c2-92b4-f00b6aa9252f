"use client";

import { type Row } from "@tanstack/react-table";
import { Lo<PERSON>, Trash } from "lucide-react";
import * as React from "react";
import { toast } from "sonner";

import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "~/components/ui/drawer";

import { useMediaQuery } from "~/lib/hooks/use-media-query";

import type { RouterOutputs } from "~/trpc/react";

interface DeleteCategoriesDialogProps extends React.ComponentPropsWithoutRef<typeof Dialog> {
  categories: Row<RouterOutputs["editor"]["getCategories"]["data"][number]>["original"][];
  showTrigger?: boolean;
  onSuccess?: () => void;
}

export function DeleteCategoriesDialog({
  categories,
  showTrigger = true,
  onSuccess,
  ...props
}: DeleteCategoriesDialogProps) {
  const [isDeletePending, startDeleteTransition] = React.useTransition();
  const isDesktop = useMediaQuery("(min-width: 640px)");

  function onDelete() {
    startDeleteTransition(async () => {});
  }

  if (isDesktop) {
    return (
      <Dialog {...props}>
        {showTrigger ? (
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <Trash className="mr-2 size-4" aria-hidden="true" />
              Xóa ({categories.length})
            </Button>
          </DialogTrigger>
        ) : null}
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bạn có chắc chắn không?</DialogTitle>
            <DialogDescription>
              Hành động này không thể hoàn tác. Điều này sẽ xóa vĩnh viễn{" "}
              <span className="font-medium">{categories.length}</span> danh mục khỏi máy chủ của chúng tôi.{" "}
              <b>
                Điều này cũng sẽ xóa {categories.reduce((prev, cate) => prev + cate.articleCount, 0)} bản tin thuộc bản
                tin này.
              </b>
            </DialogDescription>
          </DialogHeader>

          <DialogFooter className="gap-2 sm:space-x-0">
            <DialogClose asChild>
              <Button variant="outline">Hủy bỏ</Button>
            </DialogClose>
            <Button
              aria-label="Xóa các danh mục đã chọn"
              variant="destructive"
              onClick={onDelete}
              disabled={isDeletePending}
            >
              {isDeletePending && <Loader className="mr-2 size-4 animate-spin" aria-hidden="true" />}
              Xóa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer {...props}>
      {showTrigger ? (
        <DrawerTrigger asChild>
          <Button variant="outline" size="sm">
            <Trash className="mr-2 size-4" aria-hidden="true" />
            Xóa ({categories.length})
          </Button>
        </DrawerTrigger>
      ) : null}
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>Bạn có chắc chắn không?</DrawerTitle>
          <DrawerDescription>
            Hành động này không thể hoàn tác. Điều này sẽ xóa vĩnh viễn{" "}
            <span className="font-medium">{categories.length}</span> danh mục khỏi máy chủ của chúng tôi.
            <b>
              Điều này cũng sẽ xóa {categories.reduce((prev, cate) => prev + cate.articleCount, 0)} bản tin thuộc bản
              tin này.
            </b>
          </DrawerDescription>
        </DrawerHeader>

        <DrawerFooter className="gap-2 sm:space-x-0">
          <DrawerClose asChild>
            <Button variant="outline">Hủy bỏ</Button>
          </DrawerClose>
          <Button
            aria-label="Xóa các danh mục đã chọn"
            variant="destructive"
            onClick={onDelete}
            disabled={isDeletePending}
          >
            {isDeletePending && <Loader className="mr-2 size-4 animate-spin" aria-hidden="true" />}
            Xóa
          </Button>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
