/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { marked } from "marked";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import TurndownService from "turndown";

import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Separator } from "~/components/ui/separator";

import { toSentenceCase } from "~/lib/utils";
import { type UpdateArticleSchema, updateArticleSchema } from "~/lib/validate/admin-article";

import { article } from "~/server/db/schema";
import { api } from "~/trpc/react";
import { updateArticle } from "../../actions";
import TiptapToolbar from "../../create/_components/tiptap-toolbar";

interface EditArticlePageProps {
  params: Promise<{ articleId: string }>;
}

export default function EditArticlePage({ params }: EditArticlePageProps) {
  const router = useRouter();
  const turndownService = new TurndownService();
  const [articleId, setArticleId] = useState<string>("");
  const [previewContent, setPreviewContent] = useState<string>("");

  useEffect(() => {
    void params.then((p) => setArticleId(p.articleId));
  }, [params]);

  const { data: articleData, isLoading: isLoadingArticle } = api.editor.getArticleById.useQuery(
    { articleId },
    { enabled: !!articleId },
  );

  const { data: categories, isLoading: isLoadingCategories } = api.editor.getAllCategories.useQuery();

  const form = useForm<UpdateArticleSchema>({
    resolver: zodResolver(updateArticleSchema),
    defaultValues: {
      articleId: "",
      title: "",
      summary: "",
      content: "",
      status: "DRAFT",
      categoryId: "",
      thumbnail: "",
    },
  });

  const editor = useEditor({
    extensions: [StarterKit],
    editorProps: {
      attributes: {
        class:
          "min-h-[400px] w-full rounded-md border-0 bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 prose prose-sm dark:prose-invert max-w-none",
      },
    },
    content: "",
    immediatelyRender: false,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      const markdown = turndownService.turndown(html);
      form.setValue("content", markdown);
      setPreviewContent(markdown);
    },
  });

  // Update form when article data is loaded
  useEffect(() => {
    if (articleData) {
      const values = {
        articleId: articleData.articleId,
        title: articleData.title,
        summary: articleData.summary,
        content: articleData.content,
        status: articleData.status,
        categoryId: articleData.categoryId,
        thumbnail: articleData.imageUrl,
      };

      form.reset(values);
      editor?.commands.setContent(marked(articleData.content) as string);
      setPreviewContent(articleData.content);
    }
  }, [articleData, form, editor]);

  async function onSubmit(values: UpdateArticleSchema) {
    try {
      const result = await updateArticle(values);
      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success("Bản tin đã được cập nhật thành công!");
        router.push("/admin/manage/articles");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi cập nhật bản tin");
    }
  }

  if (isLoadingArticle) {
    return (
      <div className="container max-w-7xl py-6">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="text-center">
            <div className="border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"></div>
            <p className="text-muted-foreground">Đang tải bản tin...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!articleData) {
    return (
      <div className="container max-w-7xl py-6">
        <div className="text-center">
          <h1 className="text-destructive mb-2 text-2xl font-bold">Không tìm thấy bản tin</h1>
          <p className="text-muted-foreground mb-4">Bản tin bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.</p>
          <Button onClick={() => router.push("/admin/manage/articles")}>Quay lại danh sách bản tin</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container px-0 py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Chỉnh Sửa Bản Tin</h1>
        <p className="text-muted-foreground">Cập nhật thông tin cho bản tin của bạn</p>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Edit Form - Left Side */}
        <div className="space-y-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <Card className="p-4">
                <CardContent className="space-y-4 p-0">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tiêu đề</FormLabel>
                        <FormControl>
                          <Input placeholder="Nhập tiêu đề bản tin" className="text-lg" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="summary"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tóm tắt</FormLabel>
                        <FormControl>
                          <Input placeholder="Mô tả ngắn gọn về bản tin" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="thumbnail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ảnh bìa</FormLabel>
                          <FormControl>
                            <Input placeholder="https://example.com/image.jpg" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="categoryId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Danh mục</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Chọn danh mục" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {isLoadingCategories ? (
                                <SelectItem value="loading" disabled>
                                  Đang tải...
                                </SelectItem>
                              ) : (
                                categories?.map((category) => (
                                  <SelectItem key={category.categoryId} value={category.categoryId}>
                                    {category.title}
                                  </SelectItem>
                                ))
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Trạng thái</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Chọn trạng thái" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {article.status.enumValues.map((status) => (
                              <SelectItem key={status} value={status}>
                                {toSentenceCase(status)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              <FormField
                control={form.control}
                name="content"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="overflow-hidden rounded-md border">
                        <TiptapToolbar editor={editor} />
                        <Separator />
                        <div className="min-h-[400px] p-0">
                          <EditorContent editor={editor} {...field} />
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-4">
                <Button type="button" variant="outline" onClick={() => router.back()}>
                  Hủy
                </Button>
                <Button type="submit">Cập nhật bản tin</Button>
              </div>
            </form>
          </Form>
        </div>

        {/* Live Preview - Right Side */}
        <div className="space-y-6">
          <Card className="p-4">
            <div className="space-y-4">
              {/* Article Header */}
              <div className="space-y-2">
                <h1 className="text-2xl font-bold">{form.watch("title") || "Tiêu đề bản tin"}</h1>
                <p className="text-muted-foreground">{form.watch("summary") || "Tóm tắt bản tin sẽ hiển thị ở đây"}</p>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">{toSentenceCase(form.watch("status") || "DRAFT")}</Badge>
                  {categories?.find((c) => c.categoryId === form.watch("categoryId"))?.title && (
                    <Badge variant="secondary">
                      {categories.find((c) => c.categoryId === form.watch("categoryId"))?.title}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Article Image */}
              {form.watch("thumbnail") && (
                <div className="aspect-video w-full overflow-hidden rounded-lg border">
                  <img
                    src={form.watch("thumbnail")}
                    alt={form.watch("title") || "Article image"}
                    className="h-full w-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = "none";
                    }}
                  />
                </div>
              )}

              {/* Article Content */}
              <Separator />
              <div
                className="prose prose-sm dark:prose-invert max-w-none"
                dangerouslySetInnerHTML={{
                  __html: marked(previewContent || "Nội dung bản tin sẽ hiển thị ở đây"),
                }}
              />
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
