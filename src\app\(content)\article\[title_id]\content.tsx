"use client";

import Link from "next/link";

import { useEffect, useRef } from "react";
import ReactMarkdown from "react-markdown";

import { api } from "~/trpc/react";

export function RenderContent({ articleId, content }: { articleId: string; content: string }) {
  const markAsRead = api.user.markAsRead.useMutation();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!timeoutRef.current) timeoutRef.current = setTimeout(() => markAsRead.mutate({ articleId }), 5000);

    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ReactMarkdown
      components={{
        a: ({ children, href }) => (
          <Link className="text-blue-500 hover:text-blue-700" href={href as string} prefetch={false}>
            {children}
          </Link>
        ),
        img: ({ node: _node, alt, src, ..._prop }) => (
          <span className="flex flex-col gap-y-2">
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img src={src as string} alt={alt as string} className="my-0! rounded" {..._prop} />
            <span className="text-center text-sm text-gray-600 dark:text-gray-300">{alt}</span>
          </span>
        ),
      }}
    >
      {content}
    </ReactMarkdown>
  );
}

export function PremiumPreview() {
  return (
    <div className="flex h-32 w-full items-center justify-center bg-yellow-500 font-bold text-white">
      This is a premium preview content. You need to subscribe to read the full content.
    </div>
  );
}
