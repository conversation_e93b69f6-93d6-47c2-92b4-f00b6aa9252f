import { permission } from "~/lib/permission/server";
import { encodeArticleUrl, numberFormatter, type ReturnAwaited } from "~/lib/utils";
import { db, schema } from "~/server/db";

import { Badge } from "~/components/ui/badge";
import { Skeleton } from "~/components/ui/skeleton";

import Image from "next/image";
import Link from "next/link";

import { eq } from "drizzle-orm";
import { EyeIcon, HeartIcon } from "lucide-react";

async function getData({ excludeCategoryId }: { excludeCategoryId: string }) {
  const viewableContent = (await permission()).getUserViewableContent();

  return await db.query.article.findMany({
    where: (table, { not, eq, and, inArray }) =>
      and(not(eq(table.categoryId, excludeCategoryId)), inArray(table.status, viewableContent)),
    extras: (table, { sql }) => ({
      favoritesCount: db
        .$count(sql`${schema.favorite} "favor"`, eq(sql`"favor"."articleId"`, table.articleId))
        .as("favoritesCount"),
    }),
    limit: 4,
    orderBy: (_, { sql }) => sql`RANDOM()`,
    with: { category: { columns: { title: true } } },
  });
}

export const SidebarArticles = async ({ excludeCategoryId }: { excludeCategoryId: string }) => {
  const popularArticles = await getData({ excludeCategoryId });

  return (
    <div className="sticky top-[72px] h-max w-full space-y-2 lg:w-96">
      <h3 className="text-lg font-semibold">Bản tin phổ biến</h3>
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-1">
        {popularArticles.map((article) => (
          <SideBarArticle key={article.articleId} article={article} />
        ))}
      </div>
    </div>
  );
};

const SideBarArticle = ({ article }: { article: ReturnAwaited<typeof getData>[number] }) => {
  return (
    <Link href={encodeArticleUrl(article)} className="flex items-start gap-4" prefetch={true}>
      <div className="relative w-[100px] shrink-0">
        {article.status === "PREMIUM_PREVIEW" && (
          <Badge className="absolute top-1.5 right-1.5 z-10 bg-yellow-500 px-1 py-0.5 text-sm hover:bg-yellow-500/80">
            P
          </Badge>
        )}

        <Image
          width={100}
          height={100}
          alt={article.title}
          src={article.imageUrl}
          className="aspect-square rounded object-cover"
        />
      </div>

      <div>
        <div className="line-clamp-2 leading-tight font-medium">{article.title}</div>
        <p className="text-muted-foreground line-clamp-2 text-sm">{article.summary}</p>
        <div className="text-muted-foreground mt-1 flex items-center justify-between gap-2 text-xs">
          <div className="flex items-center gap-2">
            <div>
              <EyeIcon size={20} className="mr-1 inline" />
              <span>{numberFormatter.format(article.views)}</span>
            </div>
            <div>
              <HeartIcon size={20} className="mr-1 inline" />
              <span>{numberFormatter.format(article.favoritesCount)}</span>
            </div>
          </div>

          <Badge variant="secondary" className="px-2 py-1">
            {article.category.title}
          </Badge>
        </div>
      </div>
    </Link>
  );
};

export const SkeletonSidebarArticles = () => {
  return (
    <div className="sticky top-[72px] h-max w-96 space-y-2">
      <h3 className="text-lg font-semibold">Bản tin phổ biến</h3>

      <div className="grid gap-6">
        <SkeletonSideBarArticle />
        <SkeletonSideBarArticle />
        <SkeletonSideBarArticle />
        <SkeletonSideBarArticle />
      </div>
    </div>
  );
};

const SkeletonSideBarArticle = () => {
  return (
    <div className="flex items-start gap-4">
      <Skeleton className="aspect-square h-[100px] w-[100px] shrink-0 rounded" />

      <div className="w-full">
        <div className="space-y-2">
          <Skeleton className="h-[20px] w-full" />

          <Skeleton className="h-[14px] w-full" />
          <Skeleton className="h-[14px] w-1/2" />
        </div>

        <div className="mt-2 flex items-center justify-between gap-2">
          <Skeleton className="h-[26px] w-1/2" />
          <Skeleton className="h-[26px] w-16 rounded-full" />
        </div>
      </div>
    </div>
  );
};
