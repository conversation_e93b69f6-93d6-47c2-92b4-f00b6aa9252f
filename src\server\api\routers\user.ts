import { and, eq, sql } from "drizzle-orm";
import { z } from "zod";

import { authProcedure, createTR<PERSON>Router, publicProcedure } from "~/server/api/trpc";

export const userRouter = createTRPCRouter({
  checkFavorite: publicProcedure
    .input(z.object({ articleId: z.string() }))
    .output(z.object({ status: z.enum(["not-signed-in", "favorited", "unfavorited"]) }))
    .query(async ({ ctx, input }) => {
      if (!ctx.user.userId) return { status: "not-signed-in" };

      const result = await ctx.db.$count(
        ctx.schema.favorite,
        and(eq(ctx.schema.favorite.articleId, input.articleId), eq(ctx.schema.favorite.userId, ctx.user.userId)),
      );

      return { status: !!result ? "favorited" : "unfavorited" };
    }),

  toggleFavorite: authProcedure
    .input(z.object({ articleId: z.string(), isFavorited: z.boolean() }))
    .mutation(async ({ ctx, input }) => {
      if (!input.isFavorited) {
        await ctx.db
          .insert(ctx.schema.favorite)
          .values({ articleId: input.articleId, userId: ctx.user.userId })
          .onConflictDoNothing()
          .execute();

        return;
      }

      await ctx.db
        .delete(ctx.schema.favorite)
        .where(and(eq(ctx.schema.favorite.articleId, input.articleId), eq(ctx.schema.favorite.userId, ctx.user.userId)))
        .execute();
    }),

  markAsRead: authProcedure.input(z.object({ articleId: z.string() })).mutation(async ({ ctx, input }) => {
    await ctx.db.transaction(async (db) => {
      await db
        .insert(ctx.schema.history)
        .values({ articleId: input.articleId, userId: ctx.user.userId })
        .onConflictDoNothing();

      await db
        .update(ctx.schema.article)
        .set({ views: sql`${ctx.schema.article.views} + 1` })
        .where(eq(ctx.schema.article.articleId, input.articleId))
        .execute();
    });
  }),

  getTotalCountOf: authProcedure
    .input(z.object({ type: z.enum(["favorite", "history"]), pageSize: z.coerce.number().min(1).catch(5) }))
    .query(async ({ input, ctx }) => {
      const count = await ctx.db.$count(ctx.schema[input.type], eq(ctx.schema[input.type].userId, ctx.user.userId));
      const totalPage = Math.ceil(count / input.pageSize);

      return { count, totalPage };
    }),

  getUserData: authProcedure
    .input(
      z.object({
        type: z.enum(["favorite", "history"]),
        pageSize: z.coerce.number().min(1).catch(5),
        pageIndex: z.coerce
          .number()
          .min(1)
          .catch(1)
          .transform((v) => v - 1),
      }),
    )
    .query(async ({ input, ctx }) => {
      const table = input.type === "favorite" ? ctx.db.query.favorite : ctx.db.query.history;

      return await table.findMany({
        where: (table, { eq }) => eq(table.userId, ctx.user.userId),
        orderBy: (table, { desc }) => desc(table.createdAt),
        limit: input.pageSize,
        offset: input.pageSize * input.pageIndex,
        with: {
          article: {
            columns: { content: false },
            with: { author: { columns: { imageUrl: true, username: true } }, category: true },
            extras: (table, { sql }) => ({
              favoriteCount: ctx.db
                .$count(sql`${ctx.schema.favorite} "f"`, eq(sql`"f"."articleId"`, table.articleId))
                .as("favoriteCount"),
              commentCount: ctx.db
                .$count(sql`${ctx.schema.comment} "c"`, eq(sql`"c"."articleId"`, table.articleId))
                .as("commentCount"),
            }),
          },
        },
      });
    }),
});
