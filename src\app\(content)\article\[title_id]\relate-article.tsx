import { db, schema } from "~/server/db";
import { numberFormatter, encodeArticleUrl, type ReturnAwaited } from "~/lib/utils";

import { Badge } from "~/components/ui/badge";
import { Skeleton } from "~/components/ui/skeleton";

import Image from "next/image";
import Link from "next/link";

import { EyeIcon, HeartIcon } from "lucide-react";
import { eq, sql } from "drizzle-orm";
import { permission } from "~/lib/permission/server";

async function fetchData({ categoryId, excludeId }: { categoryId: string; excludeId: string }) {
  const viewableContent = (await permission()).getUserViewableContent();

  const articles = db.query.article.findMany({
    where: (table, { eq, not, and, inArray }) =>
      and(
        inArray(table.status, viewableContent),
        eq(table.categoryId, categoryId),
        not(eq(table.articleId, excludeId)),
      ),
    extras: (table, {}) => ({
      favoritesCount: db
        .$count(sql`${schema.favorite} "favor"`, eq(sql`"favor"."articleId"`, table.articleId))
        .as("favoritesCount"),
    }),
    with: { category: { columns: { title: true } } },
    orderBy: sql`RANDOM()`,
    limit: 3,
  });

  return await articles;
}

export const RelatedArticles = async ({ categoryId, excludeId }: { categoryId: string; excludeId: string }) => {
  const articles = await fetchData({ categoryId, excludeId });

  return (
    <div>
      <h2 className="mb-4 text-2xl font-bold">Bản tin liên quan về {articles[0]?.category.title}</h2>
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {articles.map((article) => (
          <RelatedArticleItem article={article} key={article.articleId} />
        ))}
      </div>
    </div>
  );
};

const RelatedArticleItem = ({ article }: { article: ReturnAwaited<typeof fetchData>[number] }) => {
  return (
    <Link href={encodeArticleUrl(article)} className="group relative grid grid-rows-[max-content_1fr]" prefetch={true}>
      {article.status === "PREMIUM_PREVIEW" && (
        <Badge className="absolute top-2 right-2 z-10 bg-yellow-500 hover:bg-yellow-500/80">Premium</Badge>
      )}

      <Image
        src={article.imageUrl}
        alt="Article thumbnail"
        width={400}
        height={300}
        className="-z-10 aspect-4/3 w-full rounded object-cover transition-transform group-hover:scale-105"
      />

      <div className="mt-3 flex flex-col justify-between">
        <div>
          <h3 className="line-clamp-2 text-lg font-semibold group-hover:underline" title={article.title}>
            {article.title}
          </h3>
          <p className="text-muted-foreground line-clamp-2 text-sm">{article.summary}</p>
        </div>

        <div className="text-muted-foreground mt-2 flex items-center justify-between gap-2 text-xs">
          <div className="flex items-center gap-2">
            <div>
              <EyeIcon size={20} className="mr-1 inline" />
              <span>{numberFormatter.format(article.views)}</span>
            </div>
            <div>
              <HeartIcon size={20} className="mr-1 inline" />
              <span>{numberFormatter.format(article.favoritesCount)}</span>
            </div>
          </div>

          <Badge variant="secondary" className="px-2 py-1">
            {article.category.title}
          </Badge>
        </div>
      </div>
    </Link>
  );
};

export const SkeletonRelatedArticle = () => {
  return (
    <div className="mt-8">
      <h2 className="mb-4 text-2xl font-bold">Bản tin liên quan</h2>
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <SkeletonRelatedArticleItem />
        <SkeletonRelatedArticleItem />
        <SkeletonRelatedArticleItem />
      </div>
    </div>
  );
};

const SkeletonRelatedArticleItem = () => {
  return (
    <div className="group">
      <div className="relative aspect-4/3 overflow-hidden rounded">
        <Skeleton className="h-full w-full object-cover transition-transform group-hover:scale-105" />
      </div>

      <div className="mt-3">
        <div className="space-y-2">
          <Skeleton className="h-[20px] w-full" />

          <Skeleton className="h-[14px] w-full" />
          <Skeleton className="h-[14px] w-1/2" />
        </div>

        <div className="mt-2 flex items-center justify-between gap-2">
          <Skeleton className="h-[26px] w-1/2" />
          <Skeleton className="h-[26px] w-16 rounded-full" />
        </div>
      </div>
    </div>
  );
};
