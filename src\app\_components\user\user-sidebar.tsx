"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

import { BookMarked, History, User2 } from "lucide-react";

import { But<PERSON> } from "../ui/button";

export function UserSidebar() {
  const pathname = usePathname();

  return (
    <section className="flex w-full gap-2">
      <Button
        asChild
        className="flex-1 items-center justify-center"
        variant={pathname.includes("/user/profile") ? "secondary" : "outline"}
      >
        <Link href="/auth/user/profile">
          <User2 size={16} />
          <span>Cài đặt tài khoản</span>
        </Link>
      </Button>

      <Button
        asChild
        className="flex-1 items-center justify-center"
        variant={pathname.includes("/user/favorites") ? "secondary" : "outline"}
      >
        <Link href="/auth/user/favorites">
          <BookMarked size={16} />
          <span>Tin yêu thích</span>
        </Link>
      </Button>

      <Button
        asChild
        className="flex-1 items-center justify-center"
        variant={pathname.includes("/user/histories") ? "secondary" : "outline"}
      >
        <Link href="/auth/user/histories">
          <History size={16} />
          <span>L<PERSON>ch sử đọc</span>
        </Link>
      </Button>
    </section>
  );
}
