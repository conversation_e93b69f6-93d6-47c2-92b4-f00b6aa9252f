"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";

import { But<PERSON> } from "~/components/ui/button";

export default function Forbidden() {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center justify-center px-4">
      <div className="w-full max-w-md text-center">
        <h1 className="text-9xl font-bold">403</h1>

        <div className="border-border my-4 h-0.5 w-full border"></div>

        <h2 className="mb-4 text-3xl font-semibold">T<PERSON><PERSON><PERSON>ố<PERSON></h2>
        <p className="mb-8">B<PERSON>n không có quyền truy cập vào trang này.</p>

        <div className="grid grid-cols-2 space-x-4">
          <Button
            onMouseDown={() => router.back()}
            className="rounded bg-blue-600 px-6 py-2 text-white transition-colors hover:bg-blue-700"
          >
            Quay Lại
          </Button>

          <Button asChild className="rounded bg-gray-100 px-6 py-2 text-gray-700 transition-colors hover:bg-gray-200">
            <Link href="/">Trang Chủ</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
