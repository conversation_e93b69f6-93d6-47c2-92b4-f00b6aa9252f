import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type ReturnAwaited<T extends (...args: any) => unknown, NotNullable extends true | false = false> = T extends (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ...args: any
) => PromiseLike<infer R>
  ? NotNullable extends false
    ? R
    : NonNullable<R>
  : // eslint-disable-next-line @typescript-eslint/no-explicit-any
    T extends (...args: any) => infer R
    ? NotNullable extends false
      ? R
      : NonNullable<R>
    : never;

export const numberFormatter = new Intl.NumberFormat("vi-VN", {
  style: "decimal",
  minimumFractionDigits: 0,
  maximumFractionDigits: 0,
});
export const dateFormatter = new Intl.DateTimeFormat("vi", { month: "long", day: "numeric", year: "numeric" });

export function cn<T extends string>(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs)) as T;
}

export function formatDate(date: Date | string | number, opts: Intl.DateTimeFormatOptions = {}) {
  return new Intl.DateTimeFormat("vi-VN", {
    month: opts.month ?? "long",
    day: opts.day ?? "numeric",
    year: opts.year ?? "numeric",
    ...opts,
  }).format(new Date(date));
}

export function toSentenceCase(str: string) {
  return str
    .replace(/_/g, " ")
    .replace(/([A-Z])/g, "$1")
    .toLowerCase()
    .replace(/^\w/, (c) => c.toUpperCase())
    .replace(/\s+/g, " ")
    .trim();
}

export const encodeArticleUrl = (data: { articleId: string; title: string }) => {
  return `/article/${encodeURIComponent(data.title.replace(/ /g, "-"))}-${data.articleId}`;
};

export const decodeArticleUrl = (title_id: string) => {
  const decodedPath = decodeURIComponent(title_id);
  const sepertorIndex = decodedPath.lastIndexOf("-");

  const [title, id] = [decodedPath.slice(0, sepertorIndex), decodedPath.slice(sepertorIndex + 1)];
  return { title, id };
};

export const ObjectTypeSafe = {
  keys: <TObject extends Record<string, unknown>>(obj: TObject) => {
    return Object.freeze(Object.keys(obj)) as readonly [keyof TObject];
  },
  entries: <TObject extends Record<string, unknown>>(obj: TObject) => {
    return Object.freeze(Object.entries(obj)) as readonly [keyof TObject, TObject[keyof TObject]][];
  },
  values: <TObject extends Record<string, unknown>>(obj: TObject) => {
    return Object.freeze(Object.values(obj)) as readonly TObject[keyof TObject][];
  },
};
