import type { ColumnDef } from "@tanstack/react-table";

import { DataTableColumnHeader } from "~/components/table/table-column-header";

import { Actions } from "./actions-button";

import type { DataTableRowAction } from "~/lib/types";
import { dateFormatter, numberFormatter } from "~/lib/utils";
import type { RouterOutputs } from "~/trpc/react";
import { Avatar, AvatarFallback, AvatarImage } from "~/app/_components/ui/avatar";

type UserOutput = RouterOutputs["moderator"]["getUsers"]["data"][number];

export const headersToTitle = {
  username: "<PERSON><PERSON><PERSON><PERSON> dùng",
  articleCount: "Tổng bản tin",
  role: "<PERSON>ai trò",
  createdAt: "Ngày tạo",
  updatedAt: "<PERSON>ày cập nhật",
  lastSignInAt: "Lần đăng nhập cuối",

  // Role
  User: "Người dùng",
  Editor: "<PERSON><PERSON><PERSON><PERSON> tập viên",
  Moderator: "<PERSON>ư<PERSON><PERSON> giám sát",
  ChiefEditor: "<PERSON><PERSON><PERSON><PERSON> tập trưởng",
  Administrator: "<PERSON><PERSON>ản trị viên",
};

function User({ user }: { user: UserOutput }) {
  return (
    <div className="flex items-center gap-2">
      <Avatar>
        <AvatarImage src={user.imageUrl!} />
        <AvatarFallback>{user.username}</AvatarFallback>
      </Avatar>

      <div className="flex flex-col">
        <span className="font-semibold capitalize">{user.username}</span>
        <span className="text-gray-400/90">{user.email}</span>
      </div>
    </div>
  );
}

export function getColumns({
  setRowAction,
}: {
  setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<UserOutput> | null>>;
}): ColumnDef<UserOutput>[] {
  return [
    {
      accessorKey: "username",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Người dùng" />,
      cell: (row) => <User user={row.row.original} />,
    },
    {
      id: "role",
      accessorFn: ({ publicMetadata }) => publicMetadata.role,
      header: ({ column }) => <DataTableColumnHeader column={column} title="Vai trò" />,
      cell: (row) => <span className="capitalize">{headersToTitle[row.getValue<keyof typeof headersToTitle>()]}</span>,
    },
    {
      accessorKey: "articleCount",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Tổng bản tin" />,
      cell: (row) => numberFormatter.format(row.getValue<number>()),
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Ngày tạo" />,
      cell: (row) => dateFormatter.format(row.getValue<number>()),
    },
    {
      accessorKey: "updatedAt",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Ngày cập nhật" />,
      cell: (row) => dateFormatter.format(row.getValue<number>()),
    },
    {
      accessorKey: "lastSignInAt",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Lần đăng nhập cuối" />,
      cell: (row) => dateFormatter.format(row.getValue<number>()),
    },
    {
      id: "actions",
      size: 40,
      cell: ({ row }) => <Actions row={row} setRowAction={setRowAction} />,
    },
  ];
}
