import type { User } from "@clerk/nextjs/server";
import { createSearchParamsCache, parseAsInteger, parseAsString, parseAsStringEnum } from "nuqs/server";
import * as z from "zod";

import { getFiltersStateParser, getSortingStateParser } from "~/lib/parsers";
import type { ReturnAwaited } from "../utils";

export const userSearchParamsCache = createSearchParamsCache({
  pageIndex: parseAsInteger.withDefault(1),
  pageSize: parseAsInteger.withDefault(10),
  sort: getSortingStateParser<User>().withDefault([{ id: "createdAt", desc: true }]),

  username: parseAsString.withDefault(""),

  // advanced filter
  filters: getFiltersStateParser().withDefault([]),
  joinOperator: parseAsStringEnum(["and", "or"]).withDefault("and"),
});

export const updateUserSchema = z.object({
  username: z.string().min(1).max(255),
  firstname: z.string().min(1).max(255),
  lastname: z.string().min(1).max(255),

  email: z.string().email().min(1).max(255),
});

export type GetUserSchema = ReturnAwaited<typeof userSearchParamsCache.parse>;
export type UpdateUserSchema = z.infer<typeof updateUserSchema>;
