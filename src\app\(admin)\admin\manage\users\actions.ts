"use server";

import "server-only";

import { eq } from "drizzle-orm";
import { unstable_noStore } from "next/cache";

import { db, schema } from "~/server/db";

import { clerkClient } from "@clerk/nextjs/server";
import { getErrorMessage } from "~/lib/handle-error";
import { RolePermissions } from "~/lib/permission";
import { permission } from "~/lib/permission/server";
import type { UpdateCategorySchema } from "~/lib/validate/admin-category";

export async function updateUser(input: UpdateCategorySchema & { categoryId: string }) {
  unstable_noStore();

  const { has } = await permission();
  if (!has({ permissions: "user:edit" })) {
    return { data: null, error: "Bạn không có quyền chỉnh sửa người dùng này" };
  }

  try {
    await db
      .update(schema.category)
      .set({ title: input.title, description: input.description })
      .where(eq(schema.category.categoryId, input.categoryId));

    return { data: null, error: null };
  } catch (err) {
    return { data: null, error: getErrorMessage(err) };
  }
}

export async function syncUserRole(input: { userId: string }) {
  unstable_noStore();

  const { has } = await permission();
  if (!has({ permissions: "user:edit" })) {
    return { data: null, error: "Bạn không có quyền chỉnh sửa người dùng này" };
  }

  const client = await clerkClient();

  try {
    const userRole = (await client.users.getUser(input.userId)).publicMetadata.role;
    const permissions = RolePermissions[userRole];

    await client.users.updateUserMetadata(input.userId, {
      publicMetadata: { role: userRole, permissions },
    });

    return { data: null, error: null };
  } catch (err) {
    return { data: null, error: getErrorMessage(err) };
  }
}
