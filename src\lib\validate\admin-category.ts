import { createSearchParamsCache, parseAsInteger, parseAsString, parseAsStringEnum } from "nuqs/server";
import * as z from "zod";

import { getFiltersStateParser, getSortingStateParser } from "~/lib/parsers";
import { type Schema } from "~/server/db";
import type { ReturnAwaited } from "../utils";

export const categorySearchParamsCache = createSearchParamsCache({
  pageIndex: parseAsInteger.withDefault(1),
  pageSize: parseAsInteger.withDefault(10),
  sort: getSortingStateParser<Schema["category"]>().withDefault([{ id: "createdAt", desc: true }]),

  title: parseAsString.withDefault(""),

  // advanced filter
  filters: getFiltersStateParser().withDefault([]),
  joinOperator: parseAsStringEnum(["and", "or"]).withDefault("and"),
});

export const updateCategorySchema = z.object({
  title: z.string().optional(),
  description: z.string().optional(),
});

export type GetCategorySchema = ReturnAwaited<typeof categorySearchParamsCache.parse>;
export type UpdateCategorySchema = z.infer<typeof updateCategorySchema>;
