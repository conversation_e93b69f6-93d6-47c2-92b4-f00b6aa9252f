"use client";

import { SelectIcon } from "@radix-ui/react-select";
import { type Column } from "@tanstack/react-table";
import { ArrowDown, ArrowUp, Check, ChevronsUpDown, EyeOff } from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

import { cn } from "~/lib/utils";

interface DataTableColumnHeaderProps<TData, TValue> extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>;
  title: string;
}

export function DataTableColumnHeader<TData, TValue>({
  column,
  title,
  className,
}: DataTableColumnHeaderProps<TData, TValue>) {
  if (!column.getCanSort() && !column.getCanHide()) {
    return <div className={cn(className)}>{title}</div>;
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <DropdownMenu>
        <DropdownMenuTrigger
          aria-label={
            column.getIsSorted() === "desc"
              ? "Sorted descending. Click to sort ascending."
              : column.getIsSorted() === "asc"
                ? "Sorted ascending. Click to sort descending."
                : "Not sorted. Click to sort ascending."
          }
          className="flex h-8 w-fit items-center rounded-md px-2 text-xs hover:bg-background/70 data-[state=open]:hover:bg-background/70"
        >
          <span className="w-max">{title}</span>
          <SelectIcon asChild>
            {column.getCanSort() && column.getIsSorted() === "desc" ? (
              <ArrowDown className="ml-2.5 size-4" aria-hidden="true" />
            ) : column.getIsSorted() === "asc" ? (
              <ArrowUp className="ml-2.5 size-4" aria-hidden="true" />
            ) : (
              <ChevronsUpDown className="ml-2.5 size-4" aria-hidden="true" />
            )}
          </SelectIcon>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="start">
          {column.getCanSort() && (
            <>
              <DataTableMenuItem onMouseDown={() => column.toggleSorting(false)}>
                <span className="flex items-center">
                  <ArrowUp className="mr-2 size-3.5" aria-hidden="true" />
                  Tăng dần
                </span>

                {column.getIsSorted() === "asc" && <Check />}
              </DataTableMenuItem>

              <DataTableMenuItem onMouseDown={() => column.toggleSorting(true)}>
                <span className="flex items-center">
                  <ArrowDown className="mr-2 size-3.5" aria-hidden="true" />
                  Giảm dần
                </span>

                {column.getIsSorted() === "desc" && <Check />}
              </DataTableMenuItem>
            </>
          )}

          {column.getCanHide() && (
            <DataTableMenuItem onMouseDown={() => column.toggleVisibility(false)}>
              <span className="flex items-center">
                <EyeOff className="mr-2 size-3.5" aria-hidden="true" />
                Ẩn cột
              </span>
            </DataTableMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

function DataTableMenuItem({ children, className, ...props }: React.ComponentProps<typeof DropdownMenuItem>) {
  return (
    <DropdownMenuItem className={cn("w-full justify-between focus:cursor-pointer", className)} {...props}>
      {children}
    </DropdownMenuItem>
  );
}
DataTableMenuItem.displayName = "DataTableMenuItem";
