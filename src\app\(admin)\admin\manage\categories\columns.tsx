import type { ColumnDef } from "@tanstack/react-table";

import { DataTableColumnHeader } from "~/components/table/table-column-header";

import { Actions } from "./actions-button";

import type { DataTableRowAction } from "~/lib/types";
import { dateFormatter, numberFormatter } from "~/lib/utils";
import type { RouterOutputs } from "~/trpc/react";

type CategoryOutput = RouterOutputs["editor"]["getCategories"]["data"][number];

export const headersToTitle = {
  title: "Danh mục",
  description: "<PERSON>ô tả",
  articleCount: "Tổng bản tin",
  viewCount: "Tổng lượt xem",
  favoriteCount: "Tổng lượt yêu thích",
  createdAt: "Ngày tạo",
  updatedAt: "Ngày cập nhật",
};

export function getColumns({
  setRowAction,
}: {
  setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<CategoryOutput> | null>>;
}): ColumnDef<CategoryOutput>[] {
  return [
    {
      accessorKey: "title",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Tên danh mục" />,
      cell: (row) => row.getValue<string>(),
    },
    {
      accessorKey: "description",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Mô tả" />,
      cell: (row) => row.getValue<string>() || "Không có mô tả",
    },
    {
      accessorKey: "articleCount",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Tổng bản tin" />,
      cell: (row) => numberFormatter.format(row.getValue<number>()),
    },
    {
      accessorKey: "viewCount",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Tổng ượt xem" />,
      cell: (row) => numberFormatter.format(row.getValue<number>()),
    },
    {
      accessorKey: "favoriteCount",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Tổng lượt yêu thích" />,
      cell: (row) => numberFormatter.format(row.getValue<number>()),
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Ngày tạo" />,
      cell: (row) => dateFormatter.format(row.getValue<number>()),
    },
    {
      accessorKey: "updatedAt",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Ngày cập nhật" />,
      cell: (row) => dateFormatter.format(row.getValue<number>()),
    },
    {
      id: "actions",
      cell: ({ row }) => <Actions row={row} setRowAction={setRowAction} />,
      size: 40,
    },
  ];
}
