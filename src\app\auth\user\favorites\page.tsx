import { Heart } from "lucide-react";
import { Suspense } from "react";

import { api } from "~/trpc/server";

import { Loading } from "~/components/loading";
import { UserArticle } from "~/components/user/user-article";
import { UserPagination } from "~/components/user/user-pagination";

export const experimental_ppr = true;

export default async function UserHistoriesPage({
  searchParams,
}: {
  searchParams: Promise<{ pageIndex?: number; pageSize?: number }>;
}) {
  const { pageIndex, pageSize } = await searchParams;
  const { totalPage, count } = await api.user.getTotalCountOf({ type: "favorite", pageSize });

  return (
    <main className="grid grid-rows-[max-content_max-content_1fr] rounded border border-border px-6 py-7">
      <h1 className="flex items-center justify-center gap-3 text-2xl font-bold">
        <Heart className="size-8" /> <span>Bản tin yêu thích ({count})</span>
      </h1>

      <hr className="my-4 border-t border-border" />

      <section className="flex flex-col gap-y-2">
        <Suspense fallback={<Loading className="flex flex-1 items-center justify-center" />}>
          <RenderUserHistories pageIndex={pageIndex} pageSize={pageSize} />
        </Suspense>

        <UserPagination totalPage={totalPage} />
      </section>
    </main>
  );
}

async function RenderUserHistories({ pageIndex, pageSize }: { pageIndex?: number; pageSize?: number }) {
  const data = await api.user.getUserData({ type: "favorite", pageIndex, pageSize });

  return (
    <div className="flex flex-col gap-2">
      {data.map((item) => (
        <UserArticle key={item.articleId} data={item} />
      ))}
    </div>
  );
}
