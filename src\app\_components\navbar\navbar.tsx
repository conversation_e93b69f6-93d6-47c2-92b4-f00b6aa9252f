import { Protect } from "@clerk/nextjs";
import { currentUser } from "@clerk/nextjs/server";
import { BookMarked, History, Newspaper, Tag, User, User2, Users } from "lucide-react";

import Image from "next/image";
import Link from "next/link";

import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

import { permission } from "~/lib/permission/server";

import { ThemeSwitcher } from "../theme-switcher";
import { SignInBtn, SignOutBtn } from "./header-button";
import { Notifications } from "./notifications";
import { Realtime } from "./realtime";
import { SearchBar } from "./search-bar";

export async function Navbar() {
  const user = await currentUser();
  const { has } = await permission(user?.publicMetadata);

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur-sm supports-backdrop-filter:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="text-small flex h-full min-w-max items-center space-x-4">
          <Link href="/" className="flex items-center justify-center gap-1">
            <Image unoptimized alt="Bản tin 24H icon" src="/favicon.png" width={32} height={32} />
            <h1 className="text-2xl font-bold text-inherit">Bản Tin 24H</h1>
          </Link>

          <div className="h-3/4 border-l border-border" />

          <Realtime />
        </div>

        <div className="flex items-center gap-2">
          <SearchBar />
          <ThemeSwitcher />

          {user && <Notifications userId={user.id} />}

          <DropdownMenu>
            <DropdownMenuTrigger>
              <Avatar className="border border-border focus:cursor-pointer">
                <AvatarImage src={user?.imageUrl} />
                <AvatarFallback className="bg-transparent">
                  <User />
                </AvatarFallback>
              </Avatar>
            </DropdownMenuTrigger>

            {user && (
              <DropdownMenuContent aria-label="Profile Actions">
                <DropdownMenuLabel className="flex-col gap-2 py-2">
                  <p className="text-center font-semibold">Đăng nhập bằng</p>
                  <p className="text-sm">{user.primaryEmailAddress?.emailAddress}</p>
                </DropdownMenuLabel>

                <Protect
                  condition={() =>
                    has({ permissions: ["category:edit", "content:create", "access:view_moderation_panel"] })
                  }
                >
                  <DropdownMenuSeparator />

                  <Protect condition={() => has({ permissions: "content:create" })}>
                    <DropdownMenuItem className="p-0">
                      <Link className="flex w-full items-center gap-2 px-2 py-1.5" href="/admin/manage/articles">
                        <Newspaper size={16} />
                        <span>Quản lý bản tin</span>
                      </Link>
                    </DropdownMenuItem>
                  </Protect>

                  <Protect condition={() => has({ permissions: "category:edit" })}>
                    <DropdownMenuItem className="p-0">
                      <Link className="flex w-full items-center gap-2 px-2 py-1.5" href="/admin/manage/categories">
                        <Tag size={16} />
                        <span>Quản lý danh mục</span>
                      </Link>
                    </DropdownMenuItem>
                  </Protect>

                  <Protect condition={() => has({ permissions: "access:view_moderation_panel" })}>
                    <DropdownMenuItem className="p-0">
                      <Link className="flex w-full items-center gap-2 px-2 py-1.5" href="/admin/manage/users">
                        <Users size={16} />
                        <span>Quản lý người dùng</span>
                      </Link>
                    </DropdownMenuItem>
                  </Protect>
                </Protect>

                <DropdownMenuSeparator />

                <DropdownMenuItem className="p-0">
                  <Link className="flex w-full items-center gap-2 px-2 py-1.5" href="/auth/user/profile">
                    <User2 size={16} />
                    <span>Cài đặt tài khoản</span>
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuItem className="p-0">
                  <Link className="flex w-full items-center gap-2 px-2 py-1.5" href="/auth/user/favorites">
                    <BookMarked size={16} />
                    <span>Tin yêu thích</span>
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuItem className="p-0">
                  <Link className="flex w-full items-center gap-2 px-2 py-1.5" href="/auth/user/histories">
                    <History size={16} />
                    <span>Lịch sử đọc</span>
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuSeparator />

                <DropdownMenuItem className="p-0 focus:bg-destructive/70 focus:text-destructive-foreground">
                  <SignOutBtn />
                </DropdownMenuItem>
              </DropdownMenuContent>
            )}

            {!user && (
              <DropdownMenuContent aria-label="Login Actions">
                <DropdownMenuItem className="p-0">
                  <SignInBtn />
                </DropdownMenuItem>
              </DropdownMenuContent>
            )}
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
