import Link from "next/link";
import { forbidden, notFound } from "next/navigation";

import { currentUser } from "@clerk/nextjs/server";
import { Suspense } from "react";

import { CommentSection } from "~/components/comment/comments-section";
import { Badge } from "~/components/ui/badge";

import { dateFormatter, decodeArticleUrl } from "~/lib/utils";
import { db } from "~/server/db";
import { api, HydrateClient } from "~/trpc/server";

import { ArticleActionbar } from "./action-bar";
import { RenderContent } from "./content";
import { RelatedArticles, SkeletonRelatedArticle } from "./relate-article";
import { SidebarArticles, SkeletonSidebarArticles } from "./side-article";

export const experimental_ppr = true;

export default async function Page({ params }: { params: Promise<{ title_id: string }> }) {
  const { id } = decodeArticleUrl((await params).title_id);
  const user = await currentUser();

  const article = await db.query.article.findFirst({
    where: (table, { eq }) => eq(table.articleId, id),
    with: { author: true, category: true },
  });

  if (!article) notFound();

  const isPublic = article.status === "PUBLISHED" || article.status === "PREMIUM_PREVIEW";

  if (!isPublic) {
    if (!user) forbidden();
    if (!user.publicMetadata.permissions.includes("content:edit_all")) forbidden();
    else if (!user.publicMetadata.permissions.includes("content:edit_own") || user.id !== article.authorId) forbidden();
  }

  const isPremiumAndUserAllowed =
    article.status === "PREMIUM_PREVIEW" && !user?.publicMetadata.permissions.includes("features:view_premium_content");

  const displayedContent = isPremiumAndUserAllowed ? article.content.slice(0, 500).trim() + "..." : article.content;

  if (isPublic) {
    void api.user.checkFavorite.prefetch({ articleId: article.articleId });
    void api.comment.fetchCommentsForArticle.prefetch({ articleId: article.articleId, pageIndex: 0 });
  }

  return (
    <main className="container py-4">
      <section className="relative grid grid-cols-1 justify-between gap-4 lg:grid-cols-[1fr_minmax(max-content,300px)]">
        <div className="mx-auto lg:mx-0 lg:max-w-[800px]">
          <article className="prose prose-lg w-full dark:prose-invert lg:max-w-[800px]">
            <header>
              <h1 className="text-3xl font-bold">{article.title}</h1>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex gap-2">
                  <span className="font-medium capitalize">{article.author.username}</span>
                  <span>-</span>
                  <Badge variant="outline">
                    <Link href={`/category/${encodeURIComponent(article.category.title.replace(" ", "-"))}`}>
                      {article.category.title}
                    </Link>
                  </Badge>

                  {article.status === "PREMIUM_PREVIEW" && <Badge variant="premium">Premium</Badge>}
                </div>

                <time className="ml-auto" dateTime={dateFormatter.format(article.publishedAt!)}>
                  {dateFormatter.format(article.publishedAt!)}
                </time>
              </div>
            </header>

            {isPremiumAndUserAllowed ? (
              <div className={"relative max-h-[500px] overflow-hidden"}>
                <RenderContent articleId={article.articleId} content={displayedContent} />
                <div className="pointer-events-none absolute bottom-0 left-0 right-0 h-1/2 bg-linear-to-b from-transparent to-background" />
              </div>
            ) : (
              <RenderContent articleId={article.articleId} content={displayedContent} />
            )}
          </article>

          {isPremiumAndUserAllowed && (
            <section className="p-4 text-center">
              <h2 className="text-xl font-bold">Nội Dung Cao Cấp VIP PRO</h2>
              <p>Vui lòng đăng kí để mở khóa toàn bộ bài viết.</p>
            </section>
          )}
        </div>

        <Suspense fallback={<SkeletonSidebarArticles />}>
          <SidebarArticles excludeCategoryId={article.categoryId} />
        </Suspense>
      </section>

      <HydrateClient>
        <ArticleActionbar article={article} />
      </HydrateClient>

      <div className="my-2 max-w-[800px] border-t border-border" />

      <Suspense fallback={<SkeletonRelatedArticle />}>
        <RelatedArticles categoryId={article.categoryId} excludeId={article.articleId} />
      </Suspense>

      {isPublic && (
        <>
          <div className="my-2 w-full border-t border-border" />

          <HydrateClient>
            <CommentSection
              articleId={article.articleId}
              user={user ? { imageUrl: user.imageUrl, username: user.username } : null}
            />
          </HydrateClient>
        </>
      )}
    </main>
  );
}
