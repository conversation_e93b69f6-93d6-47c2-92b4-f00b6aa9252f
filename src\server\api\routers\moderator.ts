import { and, asc, desc, eq, getTableColumns, ilike, or, sql } from "drizzle-orm";
import { z } from "zod";

import { dataTableConfig } from "~/lib/data-table-config";
import { createTRPCRouter, moderatorProcedure } from "~/server/api/trpc";
import { schema } from "~/server/db";
import { ObjectTypeSafe } from "~/lib/utils";
import { filterColumns } from "~/lib/filter-columns";

export const moderatorRouter = createTRPCRouter({
  getUsers: moderatorProcedure
    .input(
      z.object({
        pageIndex: z.coerce.number().min(0).catch(0),
        pageSize: z.coerce.number().catch(10),

        username: z.string().optional(),
        filters: z.array(
          z.object({
            id: z.enum(ObjectTypeSafe.keys(getTableColumns(schema.account))),
            value: z.union([z.string(), z.array(z.string())]),
            operator: z.enum(dataTableConfig.globalOperators),
            type: z.enum(dataTableConfig.columnTypes),
            rowId: z.string(),
          }),
        ),
        sort: z.object({ id: z.string(), desc: z.boolean() }).array().nullish(),
        joinOperator: z.enum(["and", "or"]).default("and"),
      }),
    )
    .query(async ({ ctx, input }) => {
      try {
        const offset = (input.pageIndex - 1) * input.pageSize;
        const { account: a, article } = ctx.schema;

        const advancedWhere = filterColumns({
          table: a,
          filters: input.filters.filter((item) => item.value),
          joinOperator: input.joinOperator,
        });

        const where = and(
          or(
            input.username ? ilike(sql`"account_status"."email"`, `%${input.username}%`) : undefined,
            input.username ? ilike(sql`"account_status"."userId"`, `%${input.username}%`) : undefined,
            input.username ? ilike(sql`"account_status"."username"`, `%${input.username}%`) : undefined,
            input.username ? ilike(sql`"account_status"."lastName"`, `%${input.username}%`) : undefined,
            input.username ? ilike(sql`"account_status"."firstName"`, `%${input.username}%`) : undefined,
          ),
          advancedWhere,
        );

        const subQuery = ctx.db
          .select({
            ...getTableColumns(a),
            articleCount: ctx.db.$count(article, eq(a.userId, article.authorId)).as("articleCount"),
          })
          .from(a)
          .as("account_status");

        const orderBy =
          input.sort && input.sort.length > 0
            ? input.sort.map((item) => {
                const field = sql.raw(`"account_status"."${item.id}"`);
                return item.desc ? desc(field) : asc(field);
              })
            : [asc(sql`"account_status"."createdAt"`)];

        const dbData = await ctx.db
          .select()
          .from(subQuery)
          .where(where)
          .limit(input.pageSize)
          .offset(offset)
          .orderBy(...orderBy);

        const total = await ctx.db.$count(subQuery, where);
        const pageCount = Math.ceil(total / input.pageSize);

        const users = await ctx.clerkClient.users.getUserList({
          limit: input.pageSize,
          userId: dbData.map((item) => item.userId),
        });

        const data = dbData.map((item) => {
          const { lastActiveAt, updatedAt, publicMetadata, lastSignInAt, banned, locked } = users.data.find(
            (user) => user.id === item.userId,
          )!;
          return { ...item, lastActiveAt, updatedAt, publicMetadata, lastSignInAt, banned, locked };
        });

        return { data, pageCount, total };
      } catch (error) {
        console.error(error);
        return { data: [], pageCount: 0 };
      }
    }),
});
