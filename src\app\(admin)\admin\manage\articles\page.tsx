import { forbidden } from "next/navigation";
import { Suspense } from "react";

import { DateRangePicker } from "~/components/data-range-picker";
import { DataTableSkeleton } from "~/components/table/table-skeleton";
import { Skeleton } from "~/components/ui/skeleton";

import { Table } from "./table";

import { permission } from "~/lib/permission/server";
import type { SearchParams } from "~/lib/types";
import { articleSearchParamsCache } from "~/lib/validate/admin-article";

import { api } from "~/trpc/server";

export default async function Page(props: { searchParams: Promise<SearchParams> }) {
  const { has } = await permission();
  if (!has({ permissions: ["access:view_article_panel"] })) forbidden();

  const searchParams = articleSearchParamsCache.parse(await props.searchParams);
  const promises = Promise.all([
    api.editor.getArticles(searchParams),
    api.editor.getAllCategories(),
    api.editor.getStatusCounts(),
  ]);

  return (
    <section>
      <Suspense
        fallback={
          <DataTableSkeleton
            columnCount={6}
            searchableColumnCount={1}
            filterableColumnCount={2}
            cellWidths={["10rem", "auto", "12rem", "12rem", "8rem", "8rem"]}
            shrinkZero
          />
        }
      >
        <Table promises={promises}>
          <Suspense fallback={<Skeleton className="h-7 w-52" />}>
            <DateRangePicker triggerSize="sm" triggerClassName="ml-auto w-56 sm:w-60" align="end" shallow={false} />
          </Suspense>
        </Table>
      </Suspense>
    </section>
  );
}
