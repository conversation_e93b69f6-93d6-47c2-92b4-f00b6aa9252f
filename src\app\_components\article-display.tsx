import Image from "next/image";
import Link from "next/link";

import type { InferSelectModel } from "drizzle-orm";
import { Eye, Heart } from "lucide-react";

import { Badge } from "./ui/badge";
import { Skeleton } from "./ui/skeleton";

import { encodeArticleUrl } from "~/lib/utils";
import type { Schema } from "~/server/db";

export function ArticleDisplay({
  article,
}: {
  article: InferSelectModel<Schema["article"]> & { favoriteCount: number };
}) {
  return (
    <article className="content">
      <Link href={encodeArticleUrl(article)} className="group relative block" prefetch={true}>
        <div className="absolute top-2 left-0 z-10 flex w-full px-2">
          <Badge variant="outline" className="bg-background/60 space-x-2">
            <span className="flex items-center gap-2">
              <Eye className="size-4" />
              {article.views}
            </span>

            <span className="flex items-center gap-2">
              <Heart className="size-4" />
              {article.favoriteCount}
            </span>
          </Badge>

          {article.status === "PREMIUM_PREVIEW" && (
            <Badge variant="premium" className="ml-auto">
              Premium
            </Badge>
          )}
        </div>

        <Image
          src={article.imageUrl}
          alt="Article thumbnail"
          className="aspect-6/4 rounded transition-transform group-hover:scale-105"
          width={600}
          height={400}
        />

        <h3 className="mt-4 mb-2 text-xl font-bold">{article.title}</h3>
        <p className="text-zinc-500 dark:text-zinc-400">{article.summary}</p>
      </Link>
    </article>
  );
}

export function SkeletonArticleDisplay() {
  return (
    <div>
      <Skeleton className="aspect-6/4 h-64 w-full rounded" />

      <h3 className="mt-4 mb-2 text-xl font-bold">
        <Skeleton className="h-5 w-full" />
      </h3>
      <div className="space-y-2">
        <Skeleton className="h-[14px] w-full" />
        <Skeleton className="h-[14px] w-1/2" />
      </div>
    </div>
  );
}
