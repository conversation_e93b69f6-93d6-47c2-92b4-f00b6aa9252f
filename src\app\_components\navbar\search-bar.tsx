"use client";

import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";

import { LoaderCircle, Search } from "lucide-react";
import { type MouseEvent, useRef, useTransition } from "react";

import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";

export const SearchBar = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const inputRef = useRef<HTMLInputElement>(null);

  const [isLoading, startTransition] = useTransition();

  const handleSubmit = (event: MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    if (inputRef.current?.value === "") return;

    const searchQuery = new URLSearchParams();
    searchQuery.set("query", inputRef.current!.value);

    router.push("/search?" + searchQuery.toString(), { scroll: false });
    if (window.location.pathname.startsWith("/search")) router.refresh();
  };

  return (
    <form className="flex w-max rounded-md focus-within:outline focus-within:outline-offset-2 focus-within:outline-ring">
      <div className="w-40">
        <Input
          ref={inputRef}
          className="rounded-r-none border-r-0 focus-visible:ring-0"
          placeholder="Tìm kiếm"
          type="text"
          name="query"
          defaultValue={searchParams.get("query") ?? ""}
        />
      </div>

      <Button
        size="icon"
        variant="outline"
        type="submit"
        className="rounded-l-none"
        disabled={isLoading}
        onClick={(e) => startTransition(() => handleSubmit(e))}
      >
        {isLoading ? <LoaderCircle size={20} className="animate-spin" /> : <Search size={20} />}
      </Button>
    </form>
  );
};
