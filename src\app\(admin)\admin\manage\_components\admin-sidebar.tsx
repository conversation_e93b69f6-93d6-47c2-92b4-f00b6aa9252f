"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

import { Newspaper, Users, Tag } from "lucide-react";

import { Button } from "~/components/ui/button";

export function AdminSidebar() {
  const pathname = usePathname();

  return (
    <section className="flex w-full gap-2">
      <Button
        asChild
        className="flex-1 items-center justify-center"
        variant={pathname.includes("/articles") ? "secondary" : "outline"}
      >
        <Link href="/admin/manage/articles">
          <Newspaper size={16} />
          <span>Quản lý bản tin</span>
        </Link>
      </Button>

      <Button
        asChild
        className="flex-1 items-center justify-center"
        variant={pathname.includes("/categories") ? "secondary" : "outline"}
      >
        <Link href="/admin/manage/categories">
          <Tag size={16} />
          <span>Quản lý danh mục</span>
        </Link>
      </Button>

      <Button
        asChild
        className="flex-1 items-center justify-center"
        variant={pathname.includes("/users") ? "secondary" : "outline"}
      >
        <Link href="/admin/manage/users">
          <Users size={16} />
          <span>Quản lý người dùng</span>
        </Link>
      </Button>
    </section>
  );
}
