import Image from "next/image";
import type { ColumnDef } from "@tanstack/react-table";

import { DataTableColumnHeader } from "~/components/table/table-column-header";
import { Checkbox } from "~/components/ui/checkbox";
import { Badge } from "~/components/ui/badge";

import { Actions } from "./actions-button";

import { dateFormatter, numberFormatter, toSentenceCase } from "~/lib/utils";
import type { DataTableRowAction } from "~/lib/types";
import type { RouterOutputs } from "~/trpc/react";

type ArticleOutput = RouterOutputs["editor"]["getArticles"]["data"][number];

function Article({ article }: { article: ArticleOutput }) {
  return (
    <div className="flex w-max items-center gap-2">
      <div
        className="relative flex w-28 items-center justify-center rounded-md bg-zinc-100/50"
        style={{ aspectRatio: 100 / 66 }}
      >
        <Image src={article.imageUrl} alt={article.title} fill className="rounded-md" />
      </div>

      <div className="flex max-w-[50ch] flex-col gap-y-1">
        <h3 title={article.title} className="line-clamp-1 text-lg font-semibold">
          {article.title}
        </h3>
        <p title={article.summary} className="line-clamp-2 text-sm text-zinc-500 dark:text-zinc-400">
          {article.summary}
        </p>
      </div>
    </div>
  );
}

export const headersToTitle = {
  title: "Bản tin",
  categoryId: "Danh mục",
  status: "Trạng thái",
  views: "Lượt xem",
  createdAt: "Ngày tạo",
  updatedAt: "Ngày cập nhật",
};

export function getColumns({
  setRowAction,
}: {
  setRowAction: React.Dispatch<React.SetStateAction<DataTableRowAction<ArticleOutput> | null>>;
}): ColumnDef<ArticleOutput>[] {
  return [
    {
      id: "select",
      size: 40,
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-0.5"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-0.5"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "title",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Bản tin" />,
      cell: (row) => <Article article={row.row.original} />,
    },
    {
      accessorKey: "categoryId",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Danh mục" />,
      cell: ({ row }) => row.original.category.title,
    },
    {
      accessorKey: "status",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Trạng thái" />,
      cell: (row) => (
        <Badge variant="outline" className="w-max text-xs capitalize">
          {toSentenceCase(row.getValue<string>())}
        </Badge>
      ),
    },
    {
      accessorKey: "views",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Lượt xem" />,
      cell: (row) => numberFormatter.format(row.getValue<number>()),
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Ngày tạo" />,
      cell: (row) => dateFormatter.format(row.getValue<number>()),
    },
    {
      accessorKey: "updatedAt",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Ngày cập nhật" />,
      cell: (row) => dateFormatter.format(row.getValue<number>()),
    },
    { id: "actions", cell: ({ row }) => <Actions row={row} setRowAction={setRowAction} />, size: 40 },
  ];
}
