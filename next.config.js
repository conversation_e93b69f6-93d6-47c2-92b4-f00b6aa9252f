/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
import "./src/env.js";

/** @type {import("next").NextConfig} */
const config = {
  typescript: { ignoreBuildErrors: true },
  eslint: { ignoreDuringBuilds: true },
  logging: { fetches: { fullUrl: true } },
  experimental: {
    ppr: true,
    useCache: true,
    reactCompiler: true,
    authInterrupts: true,
    clientSegmentCache: true,
    turbopackPersistentCaching: true,
  },
  images: { remotePatterns: [{ protocol: "https", hostname: "*.clerk.**" }], unoptimized: true },
};

export default config;
