"use client";

import { Flag, Reply } from "lucide-react";
import { useState } from "react";
import { useSelector } from "@xstate/store/react";

import { commentStore } from "~/lib/store/comment";
import { cn, dateFormatter } from "~/lib/utils";
import { api, type RouterOutputs } from "~/trpc/react";

import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";

import { CommentTextarea } from "./comment-textarea";

type CommentProps = {
  user?: { imageUrl: string; username: string | null } | null;
  articleId: string;
};

export function CommentSection({ articleId, user }: CommentProps) {
  const [data] = api.comment.fetchCommentsForArticle.useSuspenseQuery({ articleId, pageIndex: 0 });

  return (
    <div className="w-full space-y-2 lg:max-w-[800px]">
      <div>
        <h2 className="mb-4 text-2xl font-bold"><PERSON><PERSON><PERSON> luận</h2>
        <div>
          {data?.map((comment) => <Comment key={comment.commentId} data={comment} />)}
          {data?.length === 0 && (
            <div className="flex items-center justify-center gap-4 rounded border border-border p-4 text-xl">
              <span>Hãy là người đầu tiên bình luận</span>
            </div>
          )}
        </div>
      </div>

      {user && <CommentTextarea articleId={articleId} />}
    </div>
  );
}

const Comment = ({ data }: { data: RouterOutputs["comment"]["fetchCommentsForArticle"][number] }) => {
  const [isOpen, setOpen] = useState(false);
  const replyId = useSelector(commentStore, (state) => state.context.replyId);

  return (
    <>
      <div
        className={cn("flex gap-3 rounded p-2", { "bg-muted": replyId === data.commentId, "pb-0": isOpen })}
        id={"comment-" + data.commentId}
      >
        <Avatar className="aspect-square h-12 w-12">
          <AvatarImage src={data.user.imageUrl!} alt="User avatar" />
          <AvatarFallback>{data.user.username}</AvatarFallback>
        </Avatar>

        <div className="group relative w-full flex-1">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="text-lg font-medium">{data.user.username}</div>
              <time dateTime={data.createdAt.toISOString()} className="text-sm text-muted-foreground">
                {dateFormatter.format(data.createdAt)}
              </time>
            </div>

            <div className="invisible absolute right-0 top-0 flex rounded border border-border group-hover:visible">
              {data.replyId === null && (
                <button
                  className="px-2 py-1.5"
                  title="Trả lời"
                  onMouseDown={() =>
                    commentStore.send({
                      type: "setReplyId",
                      replyId: replyId === data.commentId ? null : data.commentId,
                    })
                  }
                >
                  <Reply className="size-4" />
                </button>
              )}

              <button className="px-2 py-1.5" title="Báo cáo đánh giá vi phạm">
                <Flag className="size-4" />
              </button>
            </div>
          </div>
          <p className="text-sm">{data.content}</p>

          {data.repliesCount > 0 && (
            <div className="mt-1">
              {!isOpen && (
                <div className="flex items-center justify-between">
                  <button className="flex items-center text-sm text-muted-foreground" onMouseDown={() => setOpen(true)}>
                    <Reply size={20} className="rotate-180" />
                    {data.repliesCount} trả lời
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {isOpen && <ReplyComment commentId={data.commentId} />}
    </>
  );
};

function ReplyComment({ commentId }: { commentId: string }) {
  const { data } = api.comment.fetchReplies.useQuery({ commentId });

  return (
    <div className="pl-16">
      <blockquote className="border-default-500 flex flex-col border-l-2 pl-2">
        {data?.map((comment) => <Comment key={comment.commentId} data={comment} />)}
      </blockquote>
    </div>
  );
}
