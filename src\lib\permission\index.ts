import type { schema } from "~/server/db";

type ContentPermission =
  | "content:create"
  | "content:edit_all"
  | "content:edit_own"
  | "content:delete_own"
  | "content:delete_all"
  | "content:publish"
  | "content:submit_for_review"
  | "content:review";

type AccessPermission =
  | "access:view_moderation_panel"
  | "access:view_logs"
  | "access:view_category_panel"
  | "access:view_user_panel"
  | "access:view_article_panel";

type ModerationPermission = "user:edit" | "user:delete" | "user:suspend" | "user:ban";
type MemberPermission = "features:view_premium_content" | "features:test_beta_features";
type CategoryPermission = "category:create" | "category:edit" | "category:delete";

export type Roles = "Administrator" | "ChiefEditor" | "Moderator" | "Editor" | "Member" | "User";
export type Permission =
  | ContentPermission
  | ModerationPermission
  | MemberPermission
  | CategoryPermission
  | AccessPermission;

export const RoleOrder: Record<Roles, number> = {
  User: 0,
  Member: 0,

  Editor: 1,
  Moderator: 2,
  ChiefEditor: 3,
  Administrator: 4,
};
export const Roles = Object.keys(RoleOrder) as Roles[];

export const RoleLabels: Record<Roles, string> = {
  User: "Nguời dùng",
  Member: "Thành viên",

  Editor: "Biên tập viên",
  Moderator: "Người kiểm duyệt",
  ChiefEditor: "Biên tập viên trưởng",
  Administrator: "Quản trị viên",
};

export const RolePermissions: Record<Roles, Permission[]> = {
  Administrator: [
    "content:create",
    "content:edit_own",
    "content:edit_all",
    "content:delete_own",
    "content:delete_all",
    "content:publish",
    "content:submit_for_review",
    "content:review",

    "access:view_logs",
    "access:view_moderation_panel",
    "access:view_category_panel",
    "access:view_user_panel",
    "access:view_article_panel",

    "user:edit",
    "user:delete",
    "user:suspend",
    "user:ban",

    "category:create",
    "category:edit",
    "category:delete",

    "features:view_premium_content",
    "features:test_beta_features",
  ],
  ChiefEditor: [
    "access:view_logs",
    "access:view_category_panel",
    "access:view_article_panel",

    "content:create",
    "content:edit_own",
    "content:edit_all",
    "content:delete_own",
    "content:delete_all",
    "content:publish",
    "content:submit_for_review",
    "content:review",

    "category:create",
    "category:edit",
    "category:delete",

    "features:view_premium_content",
    "features:test_beta_features",
  ],
  Moderator: [
    "access:view_logs",
    "access:view_user_panel",
    "access:view_moderation_panel",

    "user:edit",
    "user:delete",
    "user:suspend",

    "features:view_premium_content",
    "features:test_beta_features",
  ],
  Editor: ["content:edit_own", "content:delete_own", "content:submit_for_review", "access:view_article_panel"],
  Member: ["features:view_premium_content", "features:test_beta_features"],
  User: [],
};

function has(data: { roles?: Roles | Roles[]; user: UserPublicMetadata | undefined }): boolean;
function has(data: { permissions?: Permission | Permission[]; user: UserPublicMetadata | undefined }): boolean;

function has(data: {
  roles?: Roles | Roles[];
  permissions?: Permission | Permission[];
  user: UserPublicMetadata | undefined;
}) {
  if (!data.user) return false;

  if (data.roles && data.permissions) {
    return has({ roles: data.roles, user: data.user }) && has({ permissions: data.permissions, user: data.user });
  }

  if (data.roles) {
    if (Array.isArray(data.roles)) return data.roles.includes(data.user.role);
    return data.roles === data.user.role;
  }

  if (data.permissions) {
    if (Array.isArray(data.permissions))
      return data.permissions.every((permission) => data.user!.permissions.includes(permission));
    return data.user.permissions.includes(data.permissions);
  }

  return false;
}

function isUserAllowed({ user, role }: { user?: UserPublicMetadata; role?: Roles }): boolean {
  if (role && user) return RoleOrder[role] >= RoleOrder[user.role];
  return false;
}

function getUserViewableContent({ user }: { user?: UserPublicMetadata }) {
  const isPremiumUser = user?.permissions.includes("features:view_premium_content");

  const viewableContent: (typeof schema)["article"]["$inferSelect"]["status"][] = isPremiumUser
    ? ["PUBLISHED", "PREMIUM_PREVIEW"]
    : ["PUBLISHED"];

  return viewableContent;
}

export const permission = { has, isUserAllowed, getUserViewableContent };
