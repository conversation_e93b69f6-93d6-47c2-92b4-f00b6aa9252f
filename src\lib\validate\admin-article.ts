import { createSearchParamsCache, parseAsArrayOf, parseAsInteger, parseAsString, parseAsStringEnum } from "nuqs/server";
import { z } from "zod";

import { getFiltersStateParser, getSortingStateParser } from "~/lib/parsers";
import * as schema from "~/server/db/schema";

import type { Schema } from "~/server/db";
import type { ReturnAwaited } from "../utils";

export const articleSearchParamsCache = createSearchParamsCache({
  pageIndex: parseAsInteger.withDefault(1),
  pageSize: parseAsInteger.withDefault(10),
  sort: getSortingStateParser<Schema["article"]>().withDefault([{ id: "createdAt", desc: true }]),

  title: parseAsString.withDefault(""),
  status: parseAsArrayOf(z.enum(schema.article.status.enumValues)).withDefault([]),

  from: parseAsString.withDefault(""),
  to: parseAsString.withDefault(""),

  // advanced filter
  filters: getFiltersStateParser().withDefault([]),
  joinOperator: parseAsStringEnum(["and", "or"]).withDefault("and"),
});

export const CreateArticleSchema = z.object({
  title: z.string().min(1, "Title is required"),
  summary: z.string().min(1, "Summary is required"),
  content: z.string().min(1, "Content is required"),
  imageUrl: z.string().url("Image URL must be a valid URL"),
  categoryId: z.string().min(1, "Category is required"),
});

export const updateArticleSchema = z.object({
  articleId: z.string(),

  title: z.string().optional(),
  summary: z.string().optional(),
  content: z.string().optional(),
  status: z.enum(schema.article.status.enumValues).optional(),
  categoryId: z.string().optional(),
  thumbnail: z.string().optional(),
});

export type GetArticleSchema = ReturnAwaited<typeof articleSearchParamsCache.parse>;
export type CreateArticleSchema = z.infer<typeof CreateArticleSchema>;
export type UpdateArticleSchema = z.infer<typeof updateArticleSchema>;
