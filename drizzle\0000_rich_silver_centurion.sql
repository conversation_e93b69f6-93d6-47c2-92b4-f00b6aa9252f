CREATE TABLE "bantin24h_account" (
	"userId" varchar(40) PRIMARY KEY NOT NULL,
	"email" text NOT NULL,
	"hasImage" boolean DEFAULT false NOT NULL,
	"imageUrl" text,
	"username" text NOT NULL,
	"lastName" text,
	"firstName" text,
	"isBanned" boolean DEFAULT false NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "bantin24h_account_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "bantin24h_article" (
	"articleId" varchar(25) PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"summary" text NOT NULL,
	"content" text NOT NULL,
	"imageUrl" text NOT NULL,
	"views" integer DEFAULT 0 NOT NULL,
	"status" text DEFAULT 'DRAFT' NOT NULL,
	"categoryId" varchar(40) NOT NULL,
	"authorId" varchar(40) NOT NULL,
	"approvedById" varchar(40),
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"publishedAt" timestamp,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "bantin24h_article_title_unique" UNIQUE("title")
);
--> statement-breakpoint
CREATE TABLE "bantin24h_category" (
	"categoryId" varchar(40) PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"description" text,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "bantin24h_category_title_unique" UNIQUE("title")
);
--> statement-breakpoint
CREATE TABLE "bantin24h_comment" (
	"commentId" varchar(25) PRIMARY KEY NOT NULL,
	"replyId" varchar(25),
	"userId" varchar(40) NOT NULL,
	"articleId" varchar(25) NOT NULL,
	"content" text NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "bantin24h_favorite" (
	"userId" varchar(40) NOT NULL,
	"articleId" varchar(25) NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "bantin24h_favorite_userId_articleId_pk" PRIMARY KEY("userId","articleId")
);
--> statement-breakpoint
CREATE TABLE "bantin24h_history" (
	"userId" varchar(40) NOT NULL,
	"articleId" varchar(25) NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "bantin24h_history_userId_articleId_pk" PRIMARY KEY("userId","articleId")
);
--> statement-breakpoint
CREATE TABLE "bantin24h_notification" (
	"notificationId" varchar(25) PRIMARY KEY NOT NULL,
	"userId" varchar(40),
	"articleId" varchar(25),
	"content" text NOT NULL,
	"viewed" boolean DEFAULT false NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "bantin24h_article" ADD CONSTRAINT "bantin24h_article_categoryId_bantin24h_category_categoryId_fk" FOREIGN KEY ("categoryId") REFERENCES "public"."bantin24h_category"("categoryId") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "bantin24h_article" ADD CONSTRAINT "bantin24h_article_authorId_bantin24h_account_userId_fk" FOREIGN KEY ("authorId") REFERENCES "public"."bantin24h_account"("userId") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "bantin24h_article" ADD CONSTRAINT "bantin24h_article_approvedById_bantin24h_account_userId_fk" FOREIGN KEY ("approvedById") REFERENCES "public"."bantin24h_account"("userId") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "bantin24h_comment" ADD CONSTRAINT "bantin24h_comment_replyId_bantin24h_comment_commentId_fk" FOREIGN KEY ("replyId") REFERENCES "public"."bantin24h_comment"("commentId") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bantin24h_comment" ADD CONSTRAINT "bantin24h_comment_userId_bantin24h_account_userId_fk" FOREIGN KEY ("userId") REFERENCES "public"."bantin24h_account"("userId") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "bantin24h_comment" ADD CONSTRAINT "bantin24h_comment_articleId_bantin24h_article_articleId_fk" FOREIGN KEY ("articleId") REFERENCES "public"."bantin24h_article"("articleId") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "bantin24h_favorite" ADD CONSTRAINT "bantin24h_favorite_userId_bantin24h_account_userId_fk" FOREIGN KEY ("userId") REFERENCES "public"."bantin24h_account"("userId") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "bantin24h_favorite" ADD CONSTRAINT "bantin24h_favorite_articleId_bantin24h_article_articleId_fk" FOREIGN KEY ("articleId") REFERENCES "public"."bantin24h_article"("articleId") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "bantin24h_history" ADD CONSTRAINT "bantin24h_history_userId_bantin24h_account_userId_fk" FOREIGN KEY ("userId") REFERENCES "public"."bantin24h_account"("userId") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "bantin24h_history" ADD CONSTRAINT "bantin24h_history_articleId_bantin24h_article_articleId_fk" FOREIGN KEY ("articleId") REFERENCES "public"."bantin24h_article"("articleId") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "bantin24h_notification" ADD CONSTRAINT "bantin24h_notification_userId_bantin24h_account_userId_fk" FOREIGN KEY ("userId") REFERENCES "public"."bantin24h_account"("userId") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "bantin24h_notification" ADD CONSTRAINT "bantin24h_notification_articleId_bantin24h_article_articleId_fk" FOREIGN KEY ("articleId") REFERENCES "public"."bantin24h_article"("articleId") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
CREATE INDEX "account_userId" ON "bantin24h_account" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "article_id" ON "bantin24h_article" USING btree ("articleId");--> statement-breakpoint
CREATE INDEX "category_title" ON "bantin24h_category" USING btree ("title");--> statement-breakpoint
CREATE INDEX "comment_id" ON "bantin24h_comment" USING btree ("commentId");--> statement-breakpoint
CREATE INDEX "notification_id" ON "bantin24h_notification" USING btree ("notificationId");