"use client";

import { useRouter } from "next/navigation";

import { zodResolver } from "@hookform/resolvers/zod";
import { Loader } from "lucide-react";
import * as React from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "~/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>ooter,
  SheetHeader,
  SheetTitle,
} from "~/components/ui/sheet";
import { Textarea } from "~/components/ui/textarea";

import { toSentenceCase } from "~/lib/utils";
import { type UpdateArticleSchema, updateArticleSchema } from "~/lib/validate/admin-article";
import type { RouterOutputs } from "~/trpc/react";

import { updateArticle } from "./actions";

interface UpdateArticleSheetProps extends React.ComponentPropsWithRef<typeof Sheet> {
  article: RouterOutputs["editor"]["getArticles"]["data"][number] | null;
  categories: RouterOutputs["editor"]["getAllCategories"];
}

export function UpdateArticleSheet({ article, categories, ...props }: UpdateArticleSheetProps) {
  const [isUpdatePending, startUpdateTransition] = React.useTransition();
  const router = useRouter();

  const form = useForm<UpdateArticleSchema>({
    resolver: zodResolver(updateArticleSchema),
    defaultValues: {
      articleId: article?.articleId ?? "",
      title: article?.title ?? "",
      summary: article?.summary ?? "",
      content: article?.content ?? "",
      categoryId: article?.categoryId ?? "",
      status: article?.status ?? "DRAFT",
      thumbnail: article?.imageUrl ?? "",
    },
  });

  React.useEffect(() => {
    if (article) {
      form.reset({
        articleId: article.articleId,
        title: article.title,
        summary: article.summary,
        content: article.content,
        categoryId: article.categoryId,
        status: article.status,
        thumbnail: article.imageUrl,
      });
    }
  }, [article, form]);

  function onSubmit(input: UpdateArticleSchema) {
    startUpdateTransition(async () => {
      if (!article) return;

      const { error } = await updateArticle(input);

      if (error) {
        toast.error(error);
        return;
      }

      form.reset();
      props.onOpenChange?.(false);
      toast.success("Bản tin đã được cập nhật");

      router.refresh();
    });
  }

  return (
    <Sheet {...props}>
      <SheetContent className="flex flex-col gap-6 overflow-y-auto sm:max-w-2xl">
        <SheetHeader className="text-left">
          <SheetTitle>Cập nhật bản tin</SheetTitle>
          <SheetDescription>Cập nhật chi tiết bản tin và lưu thay đổi</SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tiêu đề</FormLabel>
                  <FormControl>
                    <Input placeholder="Nhập tiêu đề bản tin" {...field} />
                  </FormControl>
                  <FormDescription>Đây sẽ là tiêu đề hiển thị cho bản tin.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="summary"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tóm tắt</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Nhập tóm tắt bản tin" className="resize-none" {...field} />
                  </FormControl>
                  <FormDescription>Tóm tắt ngắn gọn về nội dung bản tin.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="categoryId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Danh mục</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn danh mục" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.categoryId} value={category.categoryId}>
                          {category.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>Chọn danh mục phù hợp cho bản tin.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Trạng thái</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn trạng thái" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {article.status.enumValues.map((status) => (
                        <SelectItem key={status} value={status}>
                          {toSentenceCase(status)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>Trạng thái hiện tại của bản tin.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="thumbnail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Hình ảnh</FormLabel>
                  <FormControl>
                    <Input placeholder="Nhập URL hình ảnh" {...field} />
                  </FormControl>
                  <FormDescription>URL hình ảnh đại diện cho bản tin.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nội dung</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Nhập nội dung bản tin" className="min-h-[200px] resize-none" {...field} />
                  </FormControl>
                  <FormDescription>Nội dung chi tiết của bản tin.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <SheetFooter className="gap-2 pt-2 sm:space-x-0">
              <SheetClose asChild>
                <Button type="button" variant="outline">
                  Hủy
                </Button>
              </SheetClose>

              <Button type="submit" disabled={isUpdatePending}>
                {isUpdatePending && <Loader className="mr-2 size-4 animate-spin" aria-hidden="true" />}
                Lưu
              </Button>
            </SheetFooter>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
}
