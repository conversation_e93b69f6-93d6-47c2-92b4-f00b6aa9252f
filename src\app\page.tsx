import { desc, sql } from "drizzle-orm";
import { Suspense } from "react";

import { ArticleDisplay, SkeletonArticleDisplay } from "./_components/article-display";
import { Skeleton } from "./_components/ui/skeleton";

import { permission } from "~/lib/permission/server";
import { db, schema } from "~/server/db";

export const experimental_ppr = true;

export default async function Home() {
  const { getUserViewableContent } = await permission();
  const viewableContent = getUserViewableContent();

  const popularArticles = await db.query.article.findMany({
    limit: 3,
    orderBy: desc(schema.article.views),
    where: (table, { inArray }) => inArray(table.status, viewableContent),
    extras: (table, { sql }) => ({
      favoriteCount: db
        .$count(sql`${schema.favorite} "f"`, sql`"f"."articleId" = ${table.articleId}`)
        .as("favoriteCount"),
    }),
  });

  return (
    <main className="container py-4 md:px-6">
      <section className="mb-6">
        <h2 className="mb-6 text-2xl font-bold md:text-3xl">Tin phổ biến nhất</h2>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          {popularArticles.map((article) => (
            <ArticleDisplay article={article} key={article.articleId} />
          ))}
        </div>
      </section>

      <Suspense fallback={<SkeletonRandomArticles />}>
        <RandomArticles ignoreIds={popularArticles.map((a) => a.articleId)} viewableContent={viewableContent} />
      </Suspense>
    </main>
  );
}

type RandomArticleProps = {
  ignoreIds: string[];
  viewableContent: (typeof schema)["article"]["$inferSelect"]["status"][];
};

async function RandomArticles({ ignoreIds, viewableContent }: RandomArticleProps) {
  const randomArticles = await db.query.category.findMany({
    where: (table, { gte, sql, and, eq, inArray, notInArray }) =>
      gte(
        db.$count(
          sql`${schema.article} "a"`,
          and(
            eq(sql`"a"."categoryId"`, table.categoryId),
            notInArray(sql`"a"."articleId"`, ignoreIds),
            inArray(sql`"a"."status"`, viewableContent),
          ),
        ),
        3,
      ),
    with: {
      articles: {
        limit: 3,
        orderBy: sql`RANDOM()`,
        where: (table, { inArray, and, notInArray }) =>
          and(notInArray(table.articleId, ignoreIds), inArray(table.status, viewableContent)),

        extras: (table, { sql }) => ({
          favoriteCount: db
            .$count(sql`${schema.favorite} "f"`, sql`"f"."articleId" = ${table.articleId}`)
            .as("favoriteCount"),
        }),
      },
    },
    orderBy: sql`RANDOM()`,
    limit: 3,
  });

  return (
    <>
      {randomArticles.map(({ articles, title }) => (
        <section className="mb-4" key={title}>
          <h2 className="mb-4 text-2xl font-bold">{title}</h2>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            {articles.map((article) => (
              <ArticleDisplay article={article} key={article.articleId} />
            ))}
          </div>
        </section>
      ))}
    </>
  );
}

function SkeletonRandomArticles() {
  return (
    <>
      <section className="mb-8">
        <h2 className="mb-4">
          <Skeleton className="h-6 w-24" />
        </h2>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <SkeletonArticleDisplay />
          <SkeletonArticleDisplay />
          <SkeletonArticleDisplay />
        </div>
      </section>

      <section className="mb-8">
        <h2 className="mb-4">
          <Skeleton className="h-6 w-24" />
        </h2>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <SkeletonArticleDisplay />
          <SkeletonArticleDisplay />
          <SkeletonArticleDisplay />
        </div>
      </section>

      <section className="mb-8">
        <h2 className="mb-4">
          <Skeleton className="h-6 w-24" />
        </h2>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <SkeletonArticleDisplay />
          <SkeletonArticleDisplay />
          <SkeletonArticleDisplay />
        </div>
      </section>
    </>
  );
}
