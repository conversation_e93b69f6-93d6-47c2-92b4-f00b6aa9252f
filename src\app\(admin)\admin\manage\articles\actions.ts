"use server";

import "server-only";

import { eq, inArray } from "drizzle-orm";
import { unstable_noStore } from "next/cache";

import { db, schema } from "~/server/db";

import { getErrorMessage } from "~/lib/handle-error";
import { permission } from "~/lib/permission/server";
import type { UpdateArticleSchema } from "~/lib/validate/admin-article";

export async function updateArticle(input: UpdateArticleSchema) {
  unstable_noStore();
  const { has, userId } = await permission();

  const article = await db.query.article.findFirst({
    where: (table, { eq }) => eq(table.articleId, input.articleId),
    columns: { authorId: true },
  });

  if (!article) {
    return { data: null, error: "Article not found" };
  }

  if (
    !has({ permissions: "content:edit_all" }) ||
    (has({ permissions: "content:edit_own" }) && article.authorId !== userId)
  ) {
    return { data: null, error: "You don't have permission to edit this article" };
  }

  if (input.status) {
    const publishStatus = ["PREMIUM_PREVIEW", "PUBLISHED", "SCHEDULED"] as (typeof input.status)[];

    const reviewStatus = [
      "APPROVED",
      "IN_REVIEW",
      "NEEDS_REVISION",
      "REJECTED",
      "RETRACTED",
    ] as (typeof input.status)[];

    if (!has({ permissions: "content:publish" }) && publishStatus.includes(input.status)) {
      return { data: null, error: "You don't have permission to publish articles" };
    }

    if (!has({ permissions: "content:review" }) && reviewStatus.includes(input.status)) {
      return { data: null, error: "You don't have permission to review articles" };
    }
  }

  try {
    await db
      .update(schema.article)
      .set({
        categoryId: input.categoryId,
        status: input.status,
        title: input.title,
        summary: input.summary,
        content: input.content,
      })
      .where(eq(schema.article.articleId, input.articleId));

    return { data: null, error: null };
  } catch (err) {
    return { data: null, error: getErrorMessage(err) };
  }
}

export async function deleteTask(input: { articleId: string }) {
  unstable_noStore();

  const { has, userId } = await permission();

  const article = await db.query.article.findFirst({
    where: (table) => eq(table.articleId, input.articleId),
    columns: { authorId: true },
  });

  if (!article) {
    return { data: null, error: "Article not found" };
  }

  if (
    !has({ permissions: "content:delete_all" }) ||
    (has({ permissions: "content:delete_own" }) && article.authorId !== userId)
  ) {
    return { data: null, error: "You don't have permission to delete this article" };
  }

  try {
    await db.delete(schema.article).where(eq(schema.article.articleId, input.articleId));

    return { data: null, error: null };
  } catch (err) {
    return { data: null, error: getErrorMessage(err) };
  }
}

export async function deleteTasks(input: { articleIds: string[] }) {
  unstable_noStore();
  try {
    await db.delete(schema.article).where(inArray(schema.article.articleId, input.articleIds));

    return { data: null, error: null };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}
