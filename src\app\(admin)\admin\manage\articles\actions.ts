"use server";

import "server-only";

import { eq, inArray } from "drizzle-orm";
import { unstable_noStore } from "next/cache";

import { db, schema } from "~/server/db";

import { getErrorMessage } from "~/lib/handle-error";
import { permission } from "~/lib/permission/server";
import type { UpdateArticleSchema } from "~/lib/validate/admin-article";

export async function updateArticle(input: UpdateArticleSchema) {
  unstable_noStore();
  const { has, userId } = await permission();

  const article = await db.query.article.findFirst({
    where: (table, { eq }) => eq(table.articleId, input.articleId),
    columns: { authorId: true },
  });

  if (!article) {
    return { data: null, error: "Bản tin không tồn tại" };
  }

  // Fix permission logic: use && instead of ||
  if (
    !has({ permissions: "content:edit_all" }) &&
    (!has({ permissions: "content:edit_own" }) || article.authorId !== userId)
  ) {
    return { data: null, error: "Bạn không có quyền chỉnh sửa bản tin này" };
  }

  if (input.status) {
    const publishStatus = ["PREMIUM_PREVIEW", "PUBLISHED", "SCHEDULED"] as (typeof input.status)[];

    const reviewStatus = [
      "APPROVED",
      "IN_REVIEW",
      "NEEDS_REVISION",
      "REJECTED",
      "RETRACTED",
    ] as (typeof input.status)[];

    if (!has({ permissions: "content:publish" }) && publishStatus.includes(input.status)) {
      return { data: null, error: "Bạn không có quyền xuất bản bản tin" };
    }

    if (!has({ permissions: "content:review" }) && reviewStatus.includes(input.status)) {
      return { data: null, error: "Bạn không có quyền duyệt bản tin" };
    }
  }

  try {
    await db
      .update(schema.article)
      .set({
        categoryId: input.categoryId,
        status: input.status,
        title: input.title,
        summary: input.summary,
        content: input.content,
        imageUrl: input.thumbnail, // Add missing imageUrl field
      })
      .where(eq(schema.article.articleId, input.articleId));

    return { data: null, error: null };
  } catch (err) {
    return { data: null, error: getErrorMessage(err) };
  }
}

export async function deleteArticle(input: { articleId: string }) {
  unstable_noStore();

  const { has, userId } = await permission();

  const article = await db.query.article.findFirst({
    where: (table) => eq(table.articleId, input.articleId),
    columns: { authorId: true },
  });

  if (!article) {
    return { data: null, error: "Bản tin không tồn tại" };
  }

  // Fix permission logic: use && instead of ||
  if (
    !has({ permissions: "content:delete_all" }) &&
    (!has({ permissions: "content:delete_own" }) || article.authorId !== userId)
  ) {
    return { data: null, error: "Bạn không có quyền xóa bản tin này" };
  }

  try {
    await db.delete(schema.article).where(eq(schema.article.articleId, input.articleId));

    return { data: null, error: null };
  } catch (err) {
    return { data: null, error: getErrorMessage(err) };
  }
}

export async function deleteArticles(input: { articleIds: string[] }) {
  unstable_noStore();

  const { has, userId } = await permission();

  // Check permission for bulk delete
  if (!has({ permissions: "content:delete_all" })) {
    // If user only has delete_own permission, check each article
    if (has({ permissions: "content:delete_own" })) {
      const articles = await db.query.article.findMany({
        where: (table) => inArray(table.articleId, input.articleIds),
        columns: { articleId: true, authorId: true },
      });

      const unauthorizedArticles = articles.filter((article) => article.authorId !== userId);
      if (unauthorizedArticles.length > 0) {
        return { data: null, error: "Bạn không có quyền xóa một số bản tin được chọn" };
      }
    } else {
      return { data: null, error: "Bạn không có quyền xóa các bản tin này" };
    }
  }

  try {
    await db.delete(schema.article).where(inArray(schema.article.articleId, input.articleIds));

    return { data: null, error: null };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}
