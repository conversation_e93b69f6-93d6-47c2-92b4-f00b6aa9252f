// Example model schema from the Drizzle docs
// https://orm.drizzle.team/docs/sql-schema-declaration

import { boolean, index, integer, primaryKey, text, timestamp, varchar, type AnyPgColumn } from "drizzle-orm/pg-core";

import { createId } from "@paralleldrive/cuid2";
import { relations } from "drizzle-orm";

import { pgTable } from "./utils";

export const account = pgTable(
  "account",
  {
    userId: varchar({ length: 40 }).primaryKey(),
    email: text().notNull().unique(),
    hasImage: boolean().notNull().default(false),
    imageUrl: text(),

    username: text().notNull(),
    lastName: text(),
    firstName: text(),

    isBanned: boolean().notNull().default(false),

    createdAt: timestamp({ mode: "date" }).notNull().defaultNow(),
    updatedAt: timestamp({ mode: "date" })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [index("account_userId").on(table.userId)],
);

export const history = pgTable(
  "history",
  {
    userId: varchar({ length: 40 })
      .notNull()
      .references(() => account.userId, { onDelete: "cascade", onUpdate: "cascade" }),
    articleId: varchar({ length: 25 })
      .notNull()
      .references(() => article.articleId, { onDelete: "cascade", onUpdate: "cascade" }),

    createdAt: timestamp({ mode: "date" }).notNull().defaultNow(),
  },
  (table) => [primaryKey({ columns: [table.userId, table.articleId] })],
);

export const favorite = pgTable(
  "favorite",
  {
    userId: varchar({ length: 40 })
      .notNull()
      .references(() => account.userId, { onDelete: "cascade", onUpdate: "cascade" }),
    articleId: varchar({ length: 25 })
      .notNull()
      .references(() => article.articleId, { onDelete: "cascade", onUpdate: "cascade" }),

    createdAt: timestamp({ mode: "date" }).notNull().defaultNow(),
  },
  (table) => [primaryKey({ columns: [table.userId, table.articleId] })],
);

export const comment = pgTable(
  "comment",
  {
    commentId: varchar({ length: 25 })
      .primaryKey()
      .$defaultFn(() => createId()),

    replyId: varchar({ length: 25 }).references((): AnyPgColumn => comment.commentId),

    userId: varchar({ length: 40 })
      .notNull()
      .references(() => account.userId, { onDelete: "set null", onUpdate: "cascade" }),
    articleId: varchar({ length: 25 })
      .notNull()
      .references(() => article.articleId, { onDelete: "cascade", onUpdate: "cascade" }),

    content: text().notNull(),

    createdAt: timestamp({ mode: "date" }).notNull().defaultNow(),
    updatedAt: timestamp({ mode: "date" })
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [index("comment_id").on(table.commentId)],
);

export const notification = pgTable(
  "notification",
  {
    notificationId: varchar({ length: 25 })
      .primaryKey()
      .$defaultFn(() => createId()),

    userId: varchar({ length: 40 }).references(() => account.userId, { onDelete: "cascade", onUpdate: "cascade" }),
    articleId: varchar({ length: 25 }).references(() => article.articleId, {
      onDelete: "set null",
      onUpdate: "cascade",
    }),

    content: text().notNull(),
    viewed: boolean().notNull().default(false),

    createdAt: timestamp({ mode: "date" }).notNull().defaultNow(),
    updatedAt: timestamp({ mode: "date" })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [index("notification_id").on(table.notificationId)],
);

export const article = pgTable(
  "article",
  {
    articleId: varchar({ length: 25 })
      .primaryKey()
      .$defaultFn(() => createId()),

    title: text().notNull().unique(),
    summary: text().notNull(),
    content: text().notNull(),
    imageUrl: text().notNull(),

    views: integer().notNull().default(0),
    status: text({
      enum: [
        "DRAFT",
        "WAITING_FOR_REVIEW",
        "IN_REVIEW",
        "NEEDS_REVISION",
        "REJECTED",
        "APPROVED",
        "SCHEDULED",
        "PREMIUM_PREVIEW",
        "PUBLISHED",
        "RETRACTED",
      ],
    })
      .notNull()
      .default("DRAFT"),

    categoryId: varchar({ length: 40 })
      .notNull()
      .references(() => category.categoryId, { onDelete: "set null", onUpdate: "cascade" }),

    authorId: varchar({ length: 40 })
      .notNull()
      .references(() => account.userId, { onDelete: "set null", onUpdate: "cascade" }),

    approvedById: varchar({ length: 40 }).references(() => account.userId, {
      onDelete: "set null",
      onUpdate: "cascade",
    }),

    createdAt: timestamp({ mode: "date" }).notNull().defaultNow(),
    publishedAt: timestamp({ mode: "date" }),
    updatedAt: timestamp({ mode: "date" })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [index("article_id").on(table.articleId)],
);

export const category = pgTable(
  "category",
  {
    categoryId: varchar({ length: 40 })
      .primaryKey()
      .$defaultFn(() => createId()),

    title: text().notNull().unique(),
    description: text(),

    createdAt: timestamp({ mode: "date" }).notNull().defaultNow(),
    updatedAt: timestamp({ mode: "date" })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [index("category_title").on(table.title)],
);

/**
 * Relationships
 */

export const accountRelationships = relations(account, ({ many }) => ({
  histories: many(history),
  favorites: many(favorite),
  comments: many(comment),
  notifications: many(notification),

  articles: many(article, { relationName: "author" }),
  approvedArticles: many(article, { relationName: "approvedBy" }),
}));

export const historyRelationships = relations(history, ({ one }) => ({
  user: one(account, { fields: [history.userId], references: [account.userId] }),
  article: one(article, { fields: [history.articleId], references: [article.articleId] }),
}));

export const favoriteRelationships = relations(favorite, ({ one }) => ({
  user: one(account, { fields: [favorite.userId], references: [account.userId] }),
  article: one(article, { fields: [favorite.articleId], references: [article.articleId] }),
}));

export const commentRelationships = relations(comment, ({ one, many }) => ({
  user: one(account, { fields: [comment.userId], references: [account.userId] }),
  article: one(article, { fields: [comment.articleId], references: [article.articleId] }),

  replies: many(comment, { relationName: "replies" }),
  repliedTo: one(comment, { fields: [comment.replyId], references: [comment.commentId], relationName: "replies" }),
}));

export const notificationRelationships = relations(notification, ({ one }) => ({
  user: one(account, { fields: [notification.userId], references: [account.userId] }),
  article: one(article, { fields: [notification.articleId], references: [article.articleId] }),
}));

export const articleRelationships = relations(article, ({ one, many }) => ({
  author: one(account, { fields: [article.authorId], references: [account.userId], relationName: "author" }),
  approvedBy: one(account, {
    fields: [article.approvedById],
    references: [account.userId],
    relationName: "approvedBy",
  }),
  category: one(category, { fields: [article.categoryId], references: [category.categoryId] }),
  comments: many(comment),
  notifications: many(notification),

  favorites: many(favorite),
  histories: many(history),
}));

export const categoryRelationships = relations(category, ({ many }) => ({ articles: many(article) }));
