import Image from "next/image";
import Link from "next/link";

import { dateFormatter, encodeArticleUrl } from "~/lib/utils";

import type { RouterOutputs } from "~/trpc/react";

export function UserArticle({ data }: { data: RouterOutputs["user"]["getUserData"][number] }) {
  return (
    <article className="contents">
      <Link href={encodeArticleUrl(data.article)} className="flex gap-2 rounded">
        <div className="relative aspect-video w-60 shrink-0 overflow-hidden rounded">
          <Image src={data.article.imageUrl} alt={data.article.title} fill unoptimized />
        </div>

        <div className="flex flex-col gap-y-1">
          <h3 className="line-clamp-1 text-lg font-bold">{data.article.title}</h3>
          <p className="line-clamp-3">{data.article.summary}</p>

          <div className="mt-auto">
            <p className="text-sm text-foreground/70">
              <span className="capitalize">- {data.article.author.username}</span> •{" "}
              {dateFormatter.format(data.article.createdAt)}
            </p>
          </div>
        </div>
      </Link>
    </article>
  );
}
