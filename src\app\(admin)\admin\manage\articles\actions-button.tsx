"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";

import type { Row } from "@tanstack/react-table";
import { Ellipsis } from "lucide-react";
import React from "react";
import { toast } from "sonner";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

import { encodeArticleUrl, toSentenceCase } from "~/lib/utils";
import type { RouterOutputs } from "~/trpc/react";
import { article } from "~/server/db/schema";

import { getErrorMessage } from "~/lib/handle-error";
import type { UpdateArticleSchema } from "~/lib/validate/admin-article";

import { updateArticle } from "./actions";

export function Actions({ row }: { row: Row<RouterOutputs["editor"]["getArticles"]["data"][number]> }) {
  const [isUpdatePending, startUpdateTransition] = React.useTransition();
  const router = useRouter();

  const keyDownHandler = React.useCallback(
    (event: React.KeyboardEvent<HTMLDivElement>) => {
      const key = event.key;
      const cmdKey = event.metaKey || event.ctrlKey;

      if (key === "v" && cmdKey) {
        event.preventDefault();
        router.push(encodeArticleUrl(row.original));
      }
    },
    [router, row.original],
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button aria-label="Open menu" variant="ghost" className="data-[state=open]:bg-muted ml-auto flex size-8 p-0">
          <Ellipsis className="size-4" aria-hidden="true" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent onKeyDown={keyDownHandler} align="end" className="w-40">
        <DropdownMenuItem>
          <Link className="flex w-full items-center gap-2" href={encodeArticleUrl(row.original)}>
            <span>Xem preview</span>
            <DropdownMenuShortcut>⌘+V</DropdownMenuShortcut>
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem onMouseDown={() => 1} className="flex items-center gap-2">
          <span>Chỉnh sửa</span>
          <DropdownMenuShortcut>⌘+E</DropdownMenuShortcut>
        </DropdownMenuItem>

        <DropdownMenuItem onMouseDown={() => 1} className="flex items-center gap-2">
          <span>Xóa bản tin</span>
          <DropdownMenuShortcut>⌘+D</DropdownMenuShortcut>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuSub>
          <DropdownMenuSubTrigger>Status</DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            <DropdownMenuRadioGroup
              value={row.original.status}
              onValueChange={(value) => {
                startUpdateTransition(() => {
                  toast.promise(
                    updateArticle({
                      articleId: row.original.articleId,
                      status: value as UpdateArticleSchema["status"],
                    }).then(() => router.refresh()),
                    {
                      loading: "Updating...",
                      success: "Status updated",
                      error: (err) => getErrorMessage(err),
                    },
                  );
                });
              }}
            >
              {article.status.enumValues.map((label) => (
                <DropdownMenuRadioItem key={label} value={label} className="capitalize" disabled={isUpdatePending}>
                  {toSentenceCase(label)}
                </DropdownMenuRadioItem>
              ))}
            </DropdownMenuRadioGroup>
          </DropdownMenuSubContent>
        </DropdownMenuSub>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
