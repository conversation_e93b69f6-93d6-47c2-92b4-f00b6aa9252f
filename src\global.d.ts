import type { Permission, Roles } from "~/lib/permission";

import type { TableHeaderState } from "~/components/table/features/table-header-title";

type OptionalStringWithSuggestion<T extends string> = T | (string & {});

export { OptionalStringWithSuggestion };

declare global {
  interface CustomJwtSessionClaims {
    metadata: {
      role: Roles;
      permissions: Permission[];
    };
  }

  interface UserPublicMetadata {
    role: Roles;
    permissions: Permission[];
  }
}

declare module "@tanstack/react-table" {
  //merge our new feature's state with the existing table state
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  interface TableState extends TableHeaderState {}

  //merge our new feature's instance APIs with the existing table instance APIs
  // interface Table<TData extends RowData> extends TableHeaderInstance<TData> {}
  // if you need to add cell instance APIs...
  // interface Cell<TData extends RowData, TValue> extends DensityCell
  // if you need to add row instance APIs...
  // interface Row<TData extends RowData> extends DensityRow
  // if you need to add column instance APIs...
  // interface Column<TData extends RowData, TValue> extends DensityColumn
  // if you need to add header instance APIs...
  // interface Header<TData extends RowData, TValue> extends DensityHeader
}
