"use client";

import { use, useMemo, useState } from "react";

import { BaseTable } from "~/components/table/base-table";
import { TableHeaderFeature } from "~/components/table/features/table-header-title";
import { DataTableAdvancedToolbar } from "~/components/table/table-advanced-toolbar";

import { getColumns, headersToTitle } from "./columns";
import { UpdateCategorySheet } from "./update-sheet";

import { type RouterOutputs } from "~/trpc/react";

import { useDataTable } from "~/lib/hooks/use-data-table";
import type { DataTableAdvancedFilterField, DataTableFilterField, DataTableRowAction } from "~/lib/types";
import { DeleteCategoriesDialog } from "./delete-category";

type Props<TOutput extends RouterOutputs["editor"] = RouterOutputs["editor"]> = {
  promises: Promise<[TOutput["getCategories"]]>;
  children?: React.ReactNode;
};

export function Table(props: Props) {
  const [{ data, pageCount, total }] = use(props.promises);
  const [rowAction, setRowAction] = useState<DataTableRowAction<(typeof data)[number]> | null>(null);

  const columns = useMemo(() => getColumns({ setRowAction }), []);

  const filterFields: DataTableFilterField<(typeof data)[number]>[] = [
    {
      id: "title",
      label: "Title",
      placeholder: "Chọn lọc danh mục...",
    },
  ];

  const advancedFilterFields: DataTableAdvancedFilterField<(typeof data)[number]>[] = [
    {
      id: "articleCount",
      label: "Tổng bản tin",
      type: "number",
    },
    {
      id: "viewCount",
      label: "Tổng lượt xem",
      type: "number",
    },
    {
      id: "favoriteCount",
      label: "Tổng lượt yêu thích",
      type: "number",
    },
    {
      id: "createdAt",
      label: "Ngày tạo",
      type: "date",
    },
    {
      id: "updatedAt",
      label: "Ngày cập nhật",
      type: "date",
    },
  ];

  const { table } = useDataTable({
    _features: [TableHeaderFeature],
    columns: columns,
    data: data,
    pageCount: pageCount,
    filterFields,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
      headerTitle: headersToTitle,
      total: total,
    },
    getRowId: (originalRow) => originalRow.categoryId,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <BaseTable table={table}>
        <DataTableAdvancedToolbar
          table={table}
          filterFields={filterFields}
          advancedfilterFields={advancedFilterFields}
          shallow={false}
        >
          {props.children}
        </DataTableAdvancedToolbar>
      </BaseTable>

      <UpdateCategorySheet
        open={rowAction?.type === "update"}
        onOpenChange={() => setRowAction(null)}
        category={rowAction?.row.original ?? null}
      />

      <DeleteCategoriesDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        categories={rowAction?.row.original ? [rowAction?.row.original] : []}
        showTrigger={false}
        onSuccess={() => rowAction?.row.toggleSelected(false)}
      />
    </>
  );
}
