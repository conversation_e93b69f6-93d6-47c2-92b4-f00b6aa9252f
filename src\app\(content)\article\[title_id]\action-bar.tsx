"use client";

import { Button } from "~/components/ui/button";
import { api, getBaseUrl } from "~/trpc/react";

import Link from "next/link";
import { usePathname } from "next/navigation";

import { Heart, Link as <PERSON>I<PERSON>, Loader, Twitter } from "lucide-react";
import { toast } from "sonner";

export const ArticleActionbar = ({ article }: { article: { title: string; articleId: string } }) => {
  const [data, { isFetching, refetch }] = api.user.checkFavorite.useSuspenseQuery({ articleId: article.articleId });

  const yeuThich = api.user.toggleFavorite.useMutation({
    onSuccess: () => void refetch(),
    onError: ({ message }) => toast.error("Lỗi: " + message),
  });

  const tweetUrl = new URL("https://x.com/intent/tweet");
  const currentPath = usePathname();

  tweetUrl.searchParams.set("text", article.title);
  tweetUrl.searchParams.set("url", getBaseUrl() + currentPath);

  return (
    <div className="flex max-w-[800px] items-center justify-between pt-4">
      <div className="flex gap-2">
        <Button
          size="icon"
          variant="outline"
          title={data?.status === "not-signed-in" ? "Đăng nhập để thích bài viết" : "Yêu thích bài viết"}
          disabled={yeuThich.isPending || isFetching || data?.status === "not-signed-in"}
          onMouseDown={() => {
            if (typeof data !== "undefined")
              yeuThich.mutate({ articleId: article.articleId, isFavorited: data.status === "favorited" });
          }}
        >
          {isFetching || yeuThich.isPending ? (
            <Loader size={20} className="animate-spin" />
          ) : (
            <Heart size={20} className={data.status === "favorited" ? "fill-red-600 stroke-red-600" : undefined} />
          )}
        </Button>
      </div>

      <div className="flex items-center gap-2">
        <span> Chia sẽ:</span>

        <div>
          <Button className="rounded-r-none" variant="outline" title="Chia sẽ bản tin này lên X">
            <Link href={tweetUrl.toString()} target="_blank">
              <Twitter size={20} />
            </Link>
          </Button>

          <Button
            className="rounded-l-none"
            variant="outline"
            title="Sao chép đường dẫn của bản tin này"
            onMouseDown={() => {
              toast.promise(window.navigator.clipboard.writeText(getBaseUrl() + currentPath), {
                success: "Sao chép đường dẫn thành công",
                error: "Sao chép đường dẫn thất bại",
              });
            }}
          >
            <LinkIcon size={20} />
          </Button>
        </div>
      </div>
    </div>
  );
};
