"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import TurndownService from "turndown";

import { Button } from "~/app/_components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "~/app/_components/ui/form";
import { Input } from "~/app/_components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/app/_components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "~/app/_components/ui/card";
import { Separator } from "~/app/_components/ui/separator";
import { CreateArticleSchema } from "~/lib/validate/admin-article";
import { api } from "~/trpc/react";
import TiptapToolbar from "./_components/tiptap-toolbar";

export default function CreateArticlePage() {
  const router = useRouter();
  const turndownService = new TurndownService();

  const form = useForm<CreateArticleSchema>({
    resolver: zodResolver(CreateArticleSchema),
    defaultValues: {
      title: "",
      summary: "",
      content: "",
      imageUrl: "",
      categoryId: "",
    },
  });

  const { data: categories, isLoading } = api.editor.getAllCategories.useQuery();

  const editor = useEditor({
    extensions: [StarterKit],
    editorProps: {
      attributes: {
        class:
          "min-h-[400px] w-full rounded-md border-0 bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 prose prose-sm dark:prose-invert max-w-none",
      },
    },
    content: "",
    immediatelyRender: false,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      const markdown = turndownService.turndown(html);
      form.setValue("content", markdown);
    },
  });

  const createPost = api.editor.createArticle.useMutation({
    onSuccess: () => {
      toast.success("Article created successfully!");
      router.push("/admin/manage/articles");
    },
    onError: (error) => {
      toast.error("Failed to create article: " + error.message);
    },
  });

  function onSubmit(values: CreateArticleSchema) {
    createPost.mutate(values);
  }

  return (
    <div className="container max-w-4xl py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Tạo Bản Tin</h1>
        <p className="text-muted-foreground">Điền thông tin cho bản tin của bạn</p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Thông tin chung</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tiêu đề</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter article title" className="text-lg" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="summary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tóm tắt</FormLabel>
                    <FormControl>
                      <Input placeholder="Brief description of the article" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="imageUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ảnh bìa</FormLabel>
                      <FormControl>
                        <Input placeholder="https://example.com/image.jpg" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="categoryId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Danh mục</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {isLoading ? (
                            <SelectItem value="loading" disabled>
                              Loading...
                            </SelectItem>
                          ) : (
                            categories?.map((category) => (
                              <SelectItem key={category.categoryId} value={category.categoryId}>
                                {category.title}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="overflow-hidden rounded-md border">
                    <TiptapToolbar editor={editor} />
                    <Separator />
                    <div className="min-h-[400px] p-0">
                      <EditorContent editor={editor} {...field} />
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={() => router.back()}>
              Cancel
            </Button>
            <Button type="submit" disabled={createPost.isPending}>
              {createPost.isPending ? "Creating..." : "Create Article"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
