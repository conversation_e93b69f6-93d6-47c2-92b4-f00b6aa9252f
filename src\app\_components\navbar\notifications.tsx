import { dateFormatter, type ReturnAwaited } from "~/lib/utils";
import { db } from "~/server/db";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

import { Bell } from "lucide-react";
import { Suspense } from "react";

function getNotification({ userId }: { userId: string }) {
  return db.query.notification.findMany({
    where(fields, operators) {
      return operators.eq(fields.userId, userId);
    },
    orderBy(fields, operators) {
      return operators.desc(fields.createdAt);
    },
    with: { article: { with: { category: true } } },
    limit: 20,
  });
}

export function Notifications({ userId }: { userId: string }) {
  return (
    <Suspense
      fallback={
        <Button className="relative" variant="outline" size="icon">
          <Bell className="size-5" />
        </Button>
      }
    >
      <NotificationButton userId={userId} />
    </Suspense>
  );
}

async function NotificationButton({ userId }: { userId: string }) {
  const data = await getNotification({ userId });

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="relative" variant="outline" size="icon">
          <span className="absolute -top-2 -right-2 flex">
            <span className="relative inline-flex items-center rounded-full bg-red-500/80 px-1 text-white supports-backdrop-filter:backdrop-blur-sm">
              {data.length}
            </span>
          </span>

          <Bell className="size-5" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent className="max-w-sx">
        {data.length === 0 && <DropdownMenuLabel>Không có thông báo mới</DropdownMenuLabel>}

        {data.length > 0 &&
          data.map((notification) => (
            <DropdownMenuItem key={notification.notificationId}>
              <NotificationItem data={notification} />
            </DropdownMenuItem>
          ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function NotificationItem({ data }: { data: ReturnAwaited<typeof getNotification>[number] }) {
  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center justify-between">
        <span>{data.article?.category.title}</span>
        <span>{dateFormatter.format(data.createdAt)}</span>
      </div>

      <p className="whitespace-pre-wrap">{data.content}</p>
    </div>
  );
}
