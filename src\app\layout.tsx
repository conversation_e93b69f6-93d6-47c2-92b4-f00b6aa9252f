import "~/styles/globals.css";

import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { Inter } from "next/font/google";

import { viVN } from "@clerk/localizations";
import { ClerkProvider } from "@clerk/nextjs";
import { dark } from "@clerk/themes";

import { Footer } from "./_components/footer/footer";
import { Navbar } from "./_components/navbar/navbar";
import { Providers } from "./_components/providers";
import { Toaster } from "./_components/ui/sonner";

const fontHeading = Inter({ subsets: ["latin"], display: "swap", variable: "--font-heading" });

const fontBody = Inter({ subsets: ["latin"], display: "swap", variable: "--font-body" });

export const metadata: Metadata = {
  title: "Bản Tin 24H - Nguồn Tin Tức Đa Dạng và Chính Xác",
  description:
    "Bản Tin 24H là trang web tin tức mới nhất với nội dung đa dạng và phong phú về các sự kiện, tin tức xã hội, kinh do<PERSON>h, công nghệ và nhiều lĩnh vực khác. Cung cấp thông tin tin tức chính xác, đáng tin cậy và nhanh chóng, Bản Tin 24H giúp bạn cập nhật những tin tức mới nhất trong và ngoài nước. Hãy khám phá và trải nghiệm ngay để không bỏ lỡ bất kỳ tin tức quan trọng nào.",
  authors: { name: "Asakuri", url: "https://github.com/Noki-Asakuri" },
  keywords: ["Bản Tin 24H", "tin tức", "sự kiện", "xã hội", "kinh doanh", "công nghệ", "nội dung đa dạng"],
  icons: "/favicon.png",
};

export const viewport: Viewport = {
  themeColor: "dark",
  colorScheme: "dark light",
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <ClerkProvider localization={viVN} appearance={{ baseTheme: dark }}>
      <html lang="en" suppressHydrationWarning>
        <body className={`${fontHeading.variable} ${fontBody.variable}`}>
          <Providers>
            <div className="grid min-h-svh grid-rows-[max-content_1fr] antialiased">
              <Navbar />
              {children}
              <Footer />
            </div>
          </Providers>

          <Toaster />
        </body>
      </html>
    </ClerkProvider>
  );
}
