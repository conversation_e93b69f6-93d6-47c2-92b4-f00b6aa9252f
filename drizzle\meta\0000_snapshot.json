{"id": "ba463fcf-057d-47d9-93f4-fa2621340dc5", "prevId": "********-0000-0000-0000-********0000", "version": "7", "dialect": "postgresql", "tables": {"public.bantin24h_account": {"name": "bantin24h_account", "schema": "", "columns": {"userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(40)", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "hasImage": {"name": "hasImage", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "lastName": {"name": "lastName", "type": "text", "primaryKey": false, "notNull": false}, "firstName": {"name": "firstName", "type": "text", "primaryKey": false, "notNull": false}, "isBanned": {"name": "isBanned", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"account_userId": {"name": "account_userId", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"bantin24h_account_email_unique": {"name": "bantin24h_account_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bantin24h_article": {"name": "bantin24h_article", "schema": "", "columns": {"articleId": {"name": "articleId", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "views": {"name": "views", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'DRAFT'"}, "categoryId": {"name": "categoryId", "type": "<PERSON><PERSON><PERSON>(40)", "primaryKey": false, "notNull": true}, "authorId": {"name": "authorId", "type": "<PERSON><PERSON><PERSON>(40)", "primaryKey": false, "notNull": true}, "approvedById": {"name": "approvedById", "type": "<PERSON><PERSON><PERSON>(40)", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "publishedAt": {"name": "publishedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"article_id": {"name": "article_id", "columns": [{"expression": "articleId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"bantin24h_article_categoryId_bantin24h_category_categoryId_fk": {"name": "bantin24h_article_categoryId_bantin24h_category_categoryId_fk", "tableFrom": "bantin24h_article", "tableTo": "bantin24h_category", "columnsFrom": ["categoryId"], "columnsTo": ["categoryId"], "onDelete": "set null", "onUpdate": "cascade"}, "bantin24h_article_authorId_bantin24h_account_userId_fk": {"name": "bantin24h_article_authorId_bantin24h_account_userId_fk", "tableFrom": "bantin24h_article", "tableTo": "bantin24h_account", "columnsFrom": ["authorId"], "columnsTo": ["userId"], "onDelete": "set null", "onUpdate": "cascade"}, "bantin24h_article_approvedById_bantin24h_account_userId_fk": {"name": "bantin24h_article_approvedById_bantin24h_account_userId_fk", "tableFrom": "bantin24h_article", "tableTo": "bantin24h_account", "columnsFrom": ["approvedById"], "columnsTo": ["userId"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"bantin24h_article_title_unique": {"name": "bantin24h_article_title_unique", "nullsNotDistinct": false, "columns": ["title"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bantin24h_category": {"name": "bantin24h_category", "schema": "", "columns": {"categoryId": {"name": "categoryId", "type": "<PERSON><PERSON><PERSON>(40)", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"category_title": {"name": "category_title", "columns": [{"expression": "title", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"bantin24h_category_title_unique": {"name": "bantin24h_category_title_unique", "nullsNotDistinct": false, "columns": ["title"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bantin24h_comment": {"name": "bantin24h_comment", "schema": "", "columns": {"commentId": {"name": "commentId", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": true, "notNull": true}, "replyId": {"name": "replyId", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(40)", "primaryKey": false, "notNull": true}, "articleId": {"name": "articleId", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"comment_id": {"name": "comment_id", "columns": [{"expression": "commentId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"bantin24h_comment_replyId_bantin24h_comment_commentId_fk": {"name": "bantin24h_comment_replyId_bantin24h_comment_commentId_fk", "tableFrom": "bantin24h_comment", "tableTo": "bantin24h_comment", "columnsFrom": ["replyId"], "columnsTo": ["commentId"], "onDelete": "no action", "onUpdate": "no action"}, "bantin24h_comment_userId_bantin24h_account_userId_fk": {"name": "bantin24h_comment_userId_bantin24h_account_userId_fk", "tableFrom": "bantin24h_comment", "tableTo": "bantin24h_account", "columnsFrom": ["userId"], "columnsTo": ["userId"], "onDelete": "set null", "onUpdate": "cascade"}, "bantin24h_comment_articleId_bantin24h_article_articleId_fk": {"name": "bantin24h_comment_articleId_bantin24h_article_articleId_fk", "tableFrom": "bantin24h_comment", "tableTo": "bantin24h_article", "columnsFrom": ["articleId"], "columnsTo": ["articleId"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bantin24h_favorite": {"name": "bantin24h_favorite", "schema": "", "columns": {"userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(40)", "primaryKey": false, "notNull": true}, "articleId": {"name": "articleId", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"bantin24h_favorite_userId_bantin24h_account_userId_fk": {"name": "bantin24h_favorite_userId_bantin24h_account_userId_fk", "tableFrom": "bantin24h_favorite", "tableTo": "bantin24h_account", "columnsFrom": ["userId"], "columnsTo": ["userId"], "onDelete": "cascade", "onUpdate": "cascade"}, "bantin24h_favorite_articleId_bantin24h_article_articleId_fk": {"name": "bantin24h_favorite_articleId_bantin24h_article_articleId_fk", "tableFrom": "bantin24h_favorite", "tableTo": "bantin24h_article", "columnsFrom": ["articleId"], "columnsTo": ["articleId"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"bantin24h_favorite_userId_articleId_pk": {"name": "bantin24h_favorite_userId_articleId_pk", "columns": ["userId", "articleId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bantin24h_history": {"name": "bantin24h_history", "schema": "", "columns": {"userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(40)", "primaryKey": false, "notNull": true}, "articleId": {"name": "articleId", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"bantin24h_history_userId_bantin24h_account_userId_fk": {"name": "bantin24h_history_userId_bantin24h_account_userId_fk", "tableFrom": "bantin24h_history", "tableTo": "bantin24h_account", "columnsFrom": ["userId"], "columnsTo": ["userId"], "onDelete": "cascade", "onUpdate": "cascade"}, "bantin24h_history_articleId_bantin24h_article_articleId_fk": {"name": "bantin24h_history_articleId_bantin24h_article_articleId_fk", "tableFrom": "bantin24h_history", "tableTo": "bantin24h_article", "columnsFrom": ["articleId"], "columnsTo": ["articleId"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {"bantin24h_history_userId_articleId_pk": {"name": "bantin24h_history_userId_articleId_pk", "columns": ["userId", "articleId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bantin24h_notification": {"name": "bantin24h_notification", "schema": "", "columns": {"notificationId": {"name": "notificationId", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(40)", "primaryKey": false, "notNull": false}, "articleId": {"name": "articleId", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "viewed": {"name": "viewed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"notification_id": {"name": "notification_id", "columns": [{"expression": "notificationId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"bantin24h_notification_userId_bantin24h_account_userId_fk": {"name": "bantin24h_notification_userId_bantin24h_account_userId_fk", "tableFrom": "bantin24h_notification", "tableTo": "bantin24h_account", "columnsFrom": ["userId"], "columnsTo": ["userId"], "onDelete": "cascade", "onUpdate": "cascade"}, "bantin24h_notification_articleId_bantin24h_article_articleId_fk": {"name": "bantin24h_notification_articleId_bantin24h_article_articleId_fk", "tableFrom": "bantin24h_notification", "tableTo": "bantin24h_article", "columnsFrom": ["articleId"], "columnsTo": ["articleId"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}