"use client";

import { <PERSON><PERSON>, SendH<PERSON>zon<PERSON> } from "lucide-react";
import { type FormEvent, useState } from "react";
import { useSelector } from "@xstate/store/react";

import { cn } from "~/lib/utils";
import { commentStore } from "~/lib/store/comment";
import { api } from "~/trpc/react";

import { Button } from "~/components/ui/button";
import { Textarea } from "~/components/ui/textarea";

type ParamsType = { articleId: string };

export const CommentTextarea = ({ articleId }: ParamsType) => {
  const replyId = useSelector(commentStore, (state) => state.context.replyId);

  const [content, setNoiDung] = useState("");
  const utils = api.useUtils();

  const comment = api.comment.postComment.useMutation({
    onSuccess: async () => {
      setNoiDung("");
      commentStore.send({ type: "clearReplyId" });

      if (replyId) await utils.comment.fetchReplies.invalidate({ commentId: replyId });
      else await utils.comment.fetchCommentsForArticle.invalidate({ articleId });
    },
  });

  const submitComment = (event?: FormEvent<HTMLFormElement>) => {
    event?.preventDefault();
    comment.mutate({ articleId, content, replyId: replyId! });
  };

  return (
    <div>
      <form className="space-y-4" onSubmit={submitComment}>
        <div>
          <Textarea
            id="comment-textarea"
            value={content}
            onChange={(e) => setNoiDung(e.target.value)}
            placeholder="Viết cảm nhận của bạn về bản tin này"
            disabled={comment.isPending}
            className={cn("w-full", { "border-destructive": comment.isError })}
            rows={4}
            onKeyDown={(e) => {
              if (e.ctrlKey && e.key === "Enter" && content.length > 0) submitComment();
            }}
          />
          <div className={cn("flex justify-end", { "justify-between": comment.isError || replyId })}>
            {comment.isError && <span className="text-sm text-destructive">{comment.error.message}</span>}
            {replyId && <span className="text-sm">Bình luận đã được trả lời</span>}
            <span className="text-sm text-muted-foreground">{content.length} ký tự</span>
          </div>
        </div>

        <div className="flex justify-end">
          <Button type="submit" className="px-4 py-2 text-sm">
            {comment.isPending && <Loader className="animate-spin" />}
            {!comment.isPending && (
              <span>
                Gửi
                <SendHorizontal className="ml-2 inline-block" size={20} />
              </span>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
